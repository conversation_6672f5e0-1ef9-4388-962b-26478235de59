import { ConflictException, NotFoundException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import {
  CreateUserDto,
  PaginatedUserResponseDto,
  UpdateUserDto,
  UserFilterDto,
  UserResponseDto,
} from "./dto";
import { UserController } from "./user.controller";
import { UserService } from "./user.service";
import { AuthGuard } from "@/auth/auth.guard";
import { RoleGuard } from "@/auth/role.guard";

describe("UserController", () => {
  let controller: UserController;
  let userService: UserService;

  const mockUserService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    getFilterOptions: jest.fn(),
    remove: jest.fn(),
    update: jest.fn(),
  };

  beforeEach(async () => {
    const mockAuthGuard = {
      canActivate: jest.fn().mockReturnValue(true),
    };

    const mockRoleGuard = {
      canActivate: jest.fn().mockReturnValue(true),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        {
          provide: UserService,
          useValue: mockUserService,
        },
      ],
    })
      .overrideGuard(AuthGuard)
      .useValue(mockAuthGuard)
      .overrideGuard(RoleGuard)
      .useValue(mockRoleGuard)
      .compile();

    controller = module.get<UserController>(UserController);
    userService = module.get<UserService>(UserService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("getCurrentUserProfile", () => {
    it("should return user profile when user exists", async () => {
      const userId = "test-user-id";
      const mockRequest = {
        user: {
          oid: userId,
          role: "Engineers",
        },
      };

      const expectedUser: UserResponseDto = {
        department: "Technology",
        email: "<EMAIL>",
        id: userId,
        location: "Paris, France",
        name: "John Doe",
        role: "Engineers",
      };

      mockUserService.findOne.mockResolvedValue(expectedUser);

      const result = await controller.getCurrentUserProfile(mockRequest);

      expect(mockUserService.findOne).toHaveBeenCalledWith(userId);
      expect(mockUserService.findOne).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedUser);
    });

    it("should throw NotFoundException when user does not exist", async () => {
      const userId = "non-existent-user-id";
      const mockRequest = {
        user: {
          oid: userId,
          role: "Engineers",
        },
      };

      mockUserService.findOne.mockRejectedValue(new NotFoundException("User not found"));

      await expect(controller.getCurrentUserProfile(mockRequest)).rejects.toThrow(
        NotFoundException,
      );
      expect(mockUserService.findOne).toHaveBeenCalledWith(userId);
      expect(mockUserService.findOne).toHaveBeenCalledTimes(1);
    });

    it("should extract user ID from request.user.oid correctly", async () => {
      const userId = "specific-test-id";
      const mockRequest = {
        user: {
          oid: userId,
          role: "Admin",
          upn: "<EMAIL>",
        },
      };

      const mockUser: UserResponseDto = {
        department: "Technology",
        email: "<EMAIL>",
        id: userId,
        location: "Paris, France",
        name: "Test User",
        role: "Admin",
      };

      userService.findOne = jest.fn().mockResolvedValue(mockUser);

      const result = await controller.getCurrentUserProfile(mockRequest);

      expect(mockUserService.findOne).toHaveBeenCalledWith(userId);
      expect(result).toEqual(mockUser);
    });
  });

  describe("create", () => {
    it("should create a new user successfully", async () => {
      const createUserDto: CreateUserDto = {
        departmentId: "dept-456",
        email: "<EMAIL>",
        locationId: "loc-123",
        name: "John Doe",
        roleId: "role-789",
      };

      const expectedUser: UserResponseDto = {
        department: "Technology",
        email: "<EMAIL>",
        id: "user-123",
        location: "Paris, France",
        name: "John Doe",
        role: "Engineers",
      };

      mockUserService.create.mockResolvedValue(expectedUser);

      const result = await controller.create(createUserDto);

      expect(mockUserService.create).toHaveBeenCalledWith(createUserDto);
      expect(mockUserService.create).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedUser);
    });

    it("should throw ConflictException when email already exists", async () => {
      const createUserDto: CreateUserDto = {
        departmentId: "dept-456",
        email: "<EMAIL>",
        locationId: "loc-123",
        name: "John Doe",
        roleId: "role-789",
      };

      mockUserService.create.mockRejectedValue(
        new ConflictException("User with this email already exists"),
      );

      await expect(controller.create(createUserDto)).rejects.toThrow(ConflictException);
      expect(mockUserService.create).toHaveBeenCalledWith(createUserDto);
    });
  });

  describe("findAll", () => {
    it("should return paginated list of users", async () => {
      const filters: UserFilterDto = {
        limit: 10,
        page: 1,
        search: "john",
      };

      const expectedResponse: PaginatedUserResponseDto = {
        data: [
          {
            department: "Technology",
            email: "<EMAIL>",
            id: "user-1",
            location: "Paris, France",
            name: "John Doe",
            role: "Engineers",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockUserService.findAll.mockResolvedValue(expectedResponse);

      const result = await controller.findAll(filters);

      expect(mockUserService.findAll).toHaveBeenCalledWith(filters);
      expect(mockUserService.findAll).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResponse);
    });

    it("should return users with no filters", async () => {
      const filters: UserFilterDto = {};

      const expectedResponse: PaginatedUserResponseDto = {
        data: [],
        meta: {
          currentPage: 1,
          from: 0,
          lastPage: 0,
          perPage: 10,
          to: 0,
          total: 0,
        },
      };

      mockUserService.findAll.mockResolvedValue(expectedResponse);

      const result = await controller.findAll(filters);

      expect(mockUserService.findAll).toHaveBeenCalledWith(filters);
      expect(result).toEqual(expectedResponse);
    });
  });

  describe("getFilterOptions", () => {
    it("should return filter options for dropdowns", async () => {
      const expectedOptions = {
        departments: [{ id: "dept-1", name: "Technology" }],
        locations: [{ id: "loc-1", name: "Paris, France" }],
        roles: [{ id: "role-1", name: "Engineers" }],
      };

      mockUserService.getFilterOptions.mockResolvedValue(expectedOptions);

      const result = await controller.getFilterOptions();

      expect(mockUserService.getFilterOptions).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedOptions);
    });
  });

  describe("findOne", () => {
    it("should return user by ID", async () => {
      const userId = "user-123";
      const expectedUser: UserResponseDto = {
        department: "Technology",
        email: "<EMAIL>",
        id: userId,
        location: "Paris, France",
        name: "John Doe",
        role: "Engineers",
      };

      mockUserService.findOne.mockResolvedValue(expectedUser);

      const result = await controller.findOne(userId);

      expect(mockUserService.findOne).toHaveBeenCalledWith(userId);
      expect(mockUserService.findOne).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedUser);
    });

    it("should throw NotFoundException when user not found", async () => {
      const userId = "non-existent-id";

      mockUserService.findOne.mockRejectedValue(new NotFoundException("User not found"));

      await expect(controller.findOne(userId)).rejects.toThrow(NotFoundException);
      expect(mockUserService.findOne).toHaveBeenCalledWith(userId);
    });
  });

  describe("update", () => {
    it("should update user successfully", async () => {
      const userId = "user-123";
      const updateUserDto: UpdateUserDto = {
        email: "<EMAIL>",
        name: "John Updated",
      };

      const expectedUser: UserResponseDto = {
        department: "Technology",
        email: "<EMAIL>",
        id: userId,
        location: "Paris, France",
        name: "John Updated",
        role: "Engineers",
      };

      mockUserService.update.mockResolvedValue(expectedUser);

      const result = await controller.update(userId, updateUserDto);

      expect(mockUserService.update).toHaveBeenCalledWith(userId, updateUserDto);
      expect(mockUserService.update).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedUser);
    });

    it("should throw NotFoundException when user not found", async () => {
      const userId = "non-existent-id";
      const updateUserDto: UpdateUserDto = {
        name: "Updated Name",
      };

      mockUserService.update.mockRejectedValue(new NotFoundException("User not found"));

      await expect(controller.update(userId, updateUserDto)).rejects.toThrow(NotFoundException);
      expect(mockUserService.update).toHaveBeenCalledWith(userId, updateUserDto);
    });

    it("should throw ConflictException when email already exists", async () => {
      const userId = "user-123";
      const updateUserDto: UpdateUserDto = {
        email: "<EMAIL>",
      };

      mockUserService.update.mockRejectedValue(
        new ConflictException("User with this email already exists"),
      );

      await expect(controller.update(userId, updateUserDto)).rejects.toThrow(ConflictException);
      expect(mockUserService.update).toHaveBeenCalledWith(userId, updateUserDto);
    });
  });

  describe("remove", () => {
    it("should delete user successfully", async () => {
      const userId = "user-123";

      mockUserService.remove.mockImplementation(() => Promise.resolve());

      await controller.remove(userId);

      expect(mockUserService.remove).toHaveBeenCalledWith(userId);
      expect(mockUserService.remove).toHaveBeenCalledTimes(1);
    });

    it("should throw NotFoundException when user not found", async () => {
      const userId = "non-existent-id";

      mockUserService.remove.mockRejectedValue(new NotFoundException("User not found"));

      await expect(controller.remove(userId)).rejects.toThrow(NotFoundException);
      expect(mockUserService.remove).toHaveBeenCalledWith(userId);
    });
  });
});
