import { ConflictException, NotFoundException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { USER_ROLE } from "../role/role.types";
import {
  CreateUserDto,
  UpdateUserDto,
  UserFilterDto,
  UserResponseDto,
} from "./dto";
import { UserRepository, UserWithRelations } from "./repositories";
import { UserService } from "./user.service";
import { DepartmentService } from "@/department";
import { LocationService } from "@/location";
import { RoleService } from "@/role";

describe("UserService", () => {
  let service: UserService;

  const mockUserRepository = {
    create: jest.fn(),
    delete: jest.fn(),
    findAll: jest.fn(),
    findByEmail: jest.fn(),
    findById: jest.fn(),
    findOneOrThrow: jest.fn(),
    update: jest.fn(),
  };

  const mockRoleService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    findOneOrThrow: jest.fn(),
  };

  const mockLocationService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    findOneOrThrow: jest.fn(),
  };

  const mockDepartmentService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    findOneOrThrow: jest.fn(),
  };

  const mockUser: UserWithRelations = {
    department: {
      name: "Technology",
    },
    departmentId: "dept-456",
    email: "<EMAIL>",
    id: "user-123",
    location: {
      city: "Paris",
      country: "France",
    },
    locationId: "loc-123",
    name: "John Doe",
    role: {
      code: "ENGINEERS",
    },
    roleId: "role-789",
  };

  const mockUserResponse: UserResponseDto = {
    department: "Technology",
    email: "<EMAIL>",
    id: "user-123",
    location: "Paris, France",
    name: "John Doe",
    role: "ENGINEERS",
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
        {
          provide: RoleService,
          useValue: mockRoleService,
        },
        {
          provide: LocationService,
          useValue: mockLocationService,
        },
        {
          provide: DepartmentService,
          useValue: mockDepartmentService,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create a new user successfully", async () => {
      const createUserDto: CreateUserDto = {
        departmentId: "dept-456",
        email: "<EMAIL>",
        locationId: "loc-123",
        name: "John Doe",
        roleId: "role-789",
      };

      mockUserRepository.findByEmail.mockImplementation(() => Promise.resolve());
      mockLocationService.findOneOrThrow.mockResolvedValue({ id: "loc-123" });
      mockDepartmentService.findOneOrThrow.mockResolvedValue({ id: "dept-456" });
      mockRoleService.findOneOrThrow.mockResolvedValue({ id: "role-789" });
      mockUserRepository.create.mockResolvedValue(mockUser);

      const result = await service.create(createUserDto);

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith("<EMAIL>");
      expect(mockLocationService.findOneOrThrow).toHaveBeenCalledWith("loc-123");
      expect(mockDepartmentService.findOneOrThrow).toHaveBeenCalledWith("dept-456");
      expect(mockRoleService.findOneOrThrow).toHaveBeenCalledWith("role-789");
      expect(mockUserRepository.create).toHaveBeenCalledWith({
        departmentId: "dept-456",
        email: "<EMAIL>",
        locationId: "loc-123",
        name: "John Doe",
        roleId: "role-789",
      });
      expect(result).toEqual(mockUserResponse);
    });

    it("should throw ConflictException when email already exists", async () => {
      const createUserDto: CreateUserDto = {
        departmentId: "dept-456",
        email: "<EMAIL>",
        locationId: "loc-123",
        name: "John Doe",
        roleId: "role-789",
      };

      mockUserRepository.findByEmail.mockResolvedValue(mockUser);

      await expect(service.create(createUserDto)).rejects.toThrow(ConflictException);
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith("<EMAIL>");
    });

    it("should throw NotFoundException when location not found", async () => {
      const createUserDto: CreateUserDto = {
        departmentId: "dept-456",
        email: "<EMAIL>",
        locationId: "invalid-loc",
        name: "John Doe",
        roleId: "role-789",
      };

      mockUserRepository.findByEmail.mockImplementation(() => Promise.resolve());
      mockLocationService.findOneOrThrow.mockRejectedValue(new NotFoundException());

      await expect(service.create(createUserDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe("findAll", () => {
    it("should return paginated list of users", async () => {
      const filters: UserFilterDto = {
        limit: 10,
        page: 1,
        search: "john",
      };

      const mockRepositoryResult = {
        data: [mockUser],
        limit: 10,
        page: 1,
        total: 1,
      };

      mockUserRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const result = await service.findAll(filters);

      expect(mockUserRepository.findAll).toHaveBeenCalledWith({
        departmentId: undefined,
        limit: 10,
        locationId: undefined,
        page: 1,
        roleId: undefined,
        search: "john",
      });

      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toEqual(mockUserResponse);
      expect(result.meta.total).toBe(1);
    });

    it("should handle empty results", async () => {
      const filters: UserFilterDto = {};

      const mockRepositoryResult = {
        data: [],
        limit: 10,
        page: 1,
        total: 0,
      };

      mockUserRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const result = await service.findAll(filters);

      expect(result.data).toHaveLength(0);
      expect(result.meta.total).toBe(0);
    });

    it("should apply all filters correctly", async () => {
      const filters: UserFilterDto = {
        departmentId: "dept-456",
        limit: 5,
        locationId: "loc-123",
        page: 2,
        roleId: "role-789",
        search: "doe",
      };

      mockUserRepository.findAll.mockResolvedValue({
        data: [],
        limit: 5,
        page: 2,
        total: 0,
      });

      await service.findAll(filters);

      expect(mockUserRepository.findAll).toHaveBeenCalledWith({
        departmentId: "dept-456",
        limit: 5,
        locationId: "loc-123",
        page: 2,
        roleId: "role-789",
        search: "doe",
      });
    });

    it("should apply roleCode filter correctly", async () => {
      const filters: UserFilterDto = {
        roleCode: USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS,
      };

      mockUserRepository.findAll.mockResolvedValue({
        data: [],
        limit: 10,
        page: 1,
        total: 0,
      });

      await service.findAll(filters);

      expect(mockUserRepository.findAll).toHaveBeenCalledWith({
        departmentId: undefined,
        limit: 10,
        locationId: undefined,
        page: 1,
        roleCode: USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS,
        roleId: undefined,
        search: undefined,
      });
    });
  });

  describe("findOne", () => {
    it("should return user by ID", async () => {
      mockUserRepository.findOneOrThrow.mockResolvedValue(mockUser);

      const result = await service.findOne("user-123");

      expect(mockUserRepository.findOneOrThrow).toHaveBeenCalledWith("user-123");
      expect(result).toEqual(mockUserResponse);
    });

    it("should throw NotFoundException when user not found", async () => {
      mockUserRepository.findOneOrThrow.mockRejectedValue(new Error("User not found"));

      await expect(service.findOne("non-existent")).rejects.toThrow(Error);
      expect(mockUserRepository.findOneOrThrow).toHaveBeenCalledWith("non-existent");
    });
  });

  describe("update", () => {
    const updateUserDto: UpdateUserDto = {
      email: "<EMAIL>",
      name: "John Updated",
    };

    it("should update user successfully", async () => {
      const updatedUser = { ...mockUser, email: "<EMAIL>", name: "John Updated" };

      mockUserRepository.findOneOrThrow.mockResolvedValue(mockUser);
      mockUserRepository.findByEmail.mockImplementation(() => Promise.resolve());
      mockUserRepository.update.mockResolvedValue(updatedUser);

      const result = await service.update("user-123", updateUserDto);

      expect(mockUserRepository.findOneOrThrow).toHaveBeenCalledWith("user-123");
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith("<EMAIL>");
      expect(mockUserRepository.update).toHaveBeenCalledWith("user-123", updateUserDto);
      expect(result.name).toBe("John Updated");
      expect(result.email).toBe("<EMAIL>");
    });

    it("should throw ConflictException when email already exists", async () => {
      const existingUser = { ...mockUser, id: "different-user" };

      mockUserRepository.findOneOrThrow.mockResolvedValue(mockUser);
      mockUserRepository.findByEmail.mockResolvedValue(existingUser);

      await expect(service.update("user-123", updateUserDto)).rejects.toThrow(ConflictException);
      expect(mockUserRepository.findOneOrThrow).toHaveBeenCalledWith("user-123");
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith("<EMAIL>");
    });

    it("should allow updating email to the same email", async () => {
      const sameEmailDto: UpdateUserDto = {
        email: "<EMAIL>",
        name: "John Updated",
      };

      mockUserRepository.findOneOrThrow.mockResolvedValue(mockUser);
      mockUserRepository.update.mockResolvedValue(mockUser);

      await service.update("user-123", sameEmailDto);

      expect(mockUserRepository.findByEmail).not.toHaveBeenCalled();
      expect(mockUserRepository.update).toHaveBeenCalledWith("user-123", sameEmailDto);
    });

    it("should update with location change", async () => {
      const updateWithLocation: UpdateUserDto = {
        locationId: "new-loc-456",
      };

      mockUserRepository.findOneOrThrow.mockResolvedValue(mockUser);
      mockLocationService.findOne.mockResolvedValue({ id: "new-loc-456" });
      mockUserRepository.update.mockResolvedValue(mockUser);

      await service.update("user-123", updateWithLocation);

      expect(mockLocationService.findOne).toHaveBeenCalledWith("new-loc-456");
      expect(mockUserRepository.update).toHaveBeenCalledWith("user-123", {
        email: undefined,
        locationId: "new-loc-456",
        name: undefined,
      });
    });

    it("should throw NotFoundException when location not found", async () => {
      const updateWithInvalidLocation: UpdateUserDto = {
        locationId: "invalid-loc",
      };

      mockUserRepository.findOneOrThrow.mockResolvedValue(mockUser);
      mockLocationService.findOne.mockImplementation(() => Promise.resolve());

      await expect(service.update("user-123", updateWithInvalidLocation)).rejects.toThrow(
        NotFoundException,
      );
      expect(mockLocationService.findOne).toHaveBeenCalledWith("invalid-loc");
    });

    it("should update with department change", async () => {
      const updateWithDepartment: UpdateUserDto = {
        departmentId: "new-dept-456",
      };

      mockUserRepository.findOneOrThrow.mockResolvedValue(mockUser);
      mockDepartmentService.findOne.mockResolvedValue({ id: "new-dept-456" });
      mockUserRepository.update.mockResolvedValue(mockUser);

      await service.update("user-123", updateWithDepartment);

      expect(mockDepartmentService.findOne).toHaveBeenCalledWith("new-dept-456");
      expect(mockUserRepository.update).toHaveBeenCalledWith("user-123", {
        departmentId: "new-dept-456",
        email: undefined,
        name: undefined,
      });
    });

    it("should update with role change", async () => {
      const updateWithRole: UpdateUserDto = {
        roleId: "new-role-456",
      };

      mockUserRepository.findOneOrThrow.mockResolvedValue(mockUser);
      mockRoleService.findOne.mockResolvedValue({ id: "new-role-456" });
      mockUserRepository.update.mockResolvedValue(mockUser);

      await service.update("user-123", updateWithRole);

      expect(mockRoleService.findOne).toHaveBeenCalledWith("new-role-456");
      expect(mockUserRepository.update).toHaveBeenCalledWith("user-123", {
        email: undefined,
        name: undefined,
        roleId: "new-role-456",
      });
    });
  });

  describe("remove", () => {
    it("should delete user successfully", async () => {
      mockUserRepository.findById.mockResolvedValue(mockUser);
      mockUserRepository.delete.mockImplementation(() => Promise.resolve());

      await service.remove("user-123");

      expect(mockUserRepository.findById).toHaveBeenCalledWith("user-123");
      expect(mockUserRepository.delete).toHaveBeenCalledWith("user-123");
    });

    it("should throw NotFoundException when user not found", async () => {
      mockUserRepository.findById.mockImplementation(() => Promise.resolve());

      await expect(service.remove("non-existent")).rejects.toThrow(NotFoundException);
      expect(mockUserRepository.findById).toHaveBeenCalledWith("non-existent");
      expect(mockUserRepository.delete).not.toHaveBeenCalled();
    });
  });

  describe("getFilterOptions", () => {
    it("should return filter options for dropdowns", async () => {
      const mockRoles = [{ id: "role-1", name: "Engineers" }];
      const mockLocations = [{ city: "Paris", country: "France", id: "loc-1" }];
      const mockDepartments = [{ id: "dept-1", name: "Technology" }];

      mockRoleService.findAll.mockResolvedValue(mockRoles);
      mockLocationService.findAll.mockResolvedValue(mockLocations);
      mockDepartmentService.findAll.mockResolvedValue(mockDepartments);

      const result = await service.getFilterOptions();

      expect(mockRoleService.findAll).toHaveBeenCalled();
      expect(mockLocationService.findAll).toHaveBeenCalled();
      expect(mockDepartmentService.findAll).toHaveBeenCalled();

      expect(result).toEqual({
        departments: [{ id: "dept-1", name: "Technology" }],
        locations: [{ id: "loc-1", name: "Paris, France" }],
        roles: [{ id: "role-1", name: "Engineers" }],
      });
    });
  });
});
