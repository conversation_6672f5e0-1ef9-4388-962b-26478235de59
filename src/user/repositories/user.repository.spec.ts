import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../../prisma.service";
import { USER_ROLE } from "../../role/role.types";
import { CreateUserData, UpdateUserData, UserFilters, UserRepository } from "./user.repository";

describe("UserRepository", () => {
  let repository: UserRepository;

  const mockUser = {
    department: {
      name: "Technology",
    },
    departmentId: "dept-456",
    email: "<EMAIL>",
    id: "user-123",
    location: {
      city: "Paris",
      country: "France",
    },
    locationId: "loc-123",
    name: "<PERSON>",
    role: {
      name: "Engineers",
    },
    roleId: "role-789",
  };

  const mockPrismaUser = {
    count: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
  };

  beforeEach(async () => {
    const mockPrismaService = {
      user: mockPrismaUser,
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<UserRepository>(UserRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create a new user", async () => {
      const createUserData: CreateUserData = {
        departmentId: "dept-456",
        email: "<EMAIL>",
        locationId: "loc-123",
        name: "John Doe",
        roleId: "role-789",
      };

      mockPrismaUser.create.mockResolvedValue(mockUser);

      const result = await repository.create(createUserData);

      expect(mockPrismaUser.create).toHaveBeenCalledWith({
        data: createUserData,
        include: {
          department: true,
          location: true,
          role: true,
        },
      });
      expect(result).toEqual(mockUser);
    });
  });

  describe("findAll", () => {
    it("should return paginated users with default parameters", async () => {
      const mockUsers = [mockUser];
      const filters: UserFilters = {};

      mockPrismaUser.findMany.mockResolvedValue(mockUsers);
      mockPrismaUser.count.mockResolvedValue(1);

      const result = await repository.findAll(filters);

      expect(mockPrismaUser.findMany).toHaveBeenCalledWith({
        include: {
          department: true,
          location: true,
          role: true,
        },
        orderBy: { name: "asc" },
        skip: 0,
        take: 10,
        where: {},
      });
      expect(mockPrismaUser.count).toHaveBeenCalledWith({ where: {} });
      expect(result).toEqual({
        data: mockUsers,
        limit: 10,
        page: 1,
        total: 1,
      });
    });

    it("should apply location filter", async () => {
      const filters: UserFilters = {
        locationId: "loc-123",
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(mockPrismaUser.findMany).toHaveBeenCalledWith({
        include: {
          department: true,
          location: true,
          role: true,
        },
        orderBy: { name: "asc" },
        skip: 0,
        take: 10,
        where: { locationId: "loc-123" },
      });
    });

    it("should apply department filter", async () => {
      const filters: UserFilters = {
        departmentId: "dept-456",
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(mockPrismaUser.findMany).toHaveBeenCalledWith({
        include: {
          department: true,
          location: true,
          role: true,
        },
        orderBy: { name: "asc" },
        skip: 0,
        take: 10,
        where: { departmentId: "dept-456" },
      });
    });

    it("should apply role filter", async () => {
      const filters: UserFilters = {
        roleId: "role-789",
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(mockPrismaUser.findMany).toHaveBeenCalledWith({
        include: {
          department: true,
          location: true,
          role: true,
        },
        orderBy: { name: "asc" },
        skip: 0,
        take: 10,
        where: { roleId: "role-789" },
      });
    });

    it("should apply roleCode filter", async () => {
      const filters: UserFilters = {
        roleCode: USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS,
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(mockPrismaUser.findMany).toHaveBeenCalledWith({
        include: {
          department: true,
          location: true,
          role: true,
        },
        orderBy: { name: "asc" },
        skip: 0,
        take: 10,
        where: {
          role: {
            code: USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS,
          },
        },
      });
    });

    it("should apply search filter", async () => {
      const filters: UserFilters = {
        search: "john",
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(mockPrismaUser.findMany).toHaveBeenCalledWith({
        include: {
          department: true,
          location: true,
          role: true,
        },
        orderBy: { name: "asc" },
        skip: 0,
        take: 10,
        where: {
          OR: [
            { name: { contains: "john", mode: "insensitive" } },
            { email: { contains: "john", mode: "insensitive" } },
          ],
        },
      });
    });

    it("should apply pagination", async () => {
      const filters: UserFilters = {
        limit: 5,
        page: 2,
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(mockPrismaUser.findMany).toHaveBeenCalledWith({
        include: {
          department: true,
          location: true,
          role: true,
        },
        orderBy: { name: "asc" },
        skip: 5,
        take: 5,
        where: {},
      });
    });

    it("should apply multiple filters", async () => {
      const filters: UserFilters = {
        departmentId: "dept-456",
        limit: 5,
        locationId: "loc-123",
        page: 2,
        roleId: "role-789",
        search: "john",
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(mockPrismaUser.findMany).toHaveBeenCalledWith({
        include: {
          department: true,
          location: true,
          role: true,
        },
        orderBy: { name: "asc" },
        skip: 5,
        take: 5,
        where: {
          OR: [
            { name: { contains: "john", mode: "insensitive" } },
            { email: { contains: "john", mode: "insensitive" } },
          ],
          departmentId: "dept-456",
          locationId: "loc-123",
          roleId: "role-789",
        },
      });
    });
  });

  describe("findById", () => {
    it("should return user by ID", async () => {
      mockPrismaUser.findUnique.mockResolvedValue(mockUser);

      const result = await repository.findById("user-123");

      expect(mockPrismaUser.findUnique).toHaveBeenCalledWith({
        include: {
          department: true,
          location: true,
          role: true,
        },
        where: { id: "user-123" },
      });
      expect(result).toEqual(mockUser);
    });

    it("should return null when user not found", async () => {
      mockPrismaUser.findUnique.mockImplementation(() => Promise.resolve());

      const result = await repository.findById("non-existent");

      expect(result).toBeUndefined();
    });
  });

  describe("findOneOrThrow", () => {
    it("should return user when found", async () => {
      mockPrismaUser.findUnique.mockResolvedValue(mockUser);

      const result = await repository.findOneOrThrow("user-123");

      expect(result).toEqual(mockUser);
    });

    it("should throw error when user not found", async () => {
      mockPrismaUser.findUnique.mockImplementation(() => Promise.resolve());

      await expect(repository.findOneOrThrow("non-existent")).rejects.toThrow(
        "User with ID 'non-existent' not found",
      );
    });
  });

  describe("findByEmail", () => {
    it("should return user by email", async () => {
      const userWithoutRelations = {
        departmentId: "dept-456",
        email: "<EMAIL>",
        id: "user-123",
        locationId: "loc-123",
        name: "John Doe",
        roleId: "role-789",
      };

      mockPrismaUser.findUnique.mockResolvedValue(userWithoutRelations);

      const result = await repository.findByEmail("<EMAIL>");

      expect(mockPrismaUser.findUnique).toHaveBeenCalledWith({
        where: { email: "<EMAIL>" },
      });
      expect(result).toEqual(userWithoutRelations);
    });

    it("should return null when user not found", async () => {
      mockPrismaUser.findUnique.mockImplementation(() => Promise.resolve());

      const result = await repository.findByEmail("<EMAIL>");

      expect(result).toBeUndefined();
    });
  });

  describe("update", () => {
    it("should update user", async () => {
      const updateData: UpdateUserData = {
        email: "<EMAIL>",
        name: "John Updated",
      };

      const updatedUser = { ...mockUser, ...updateData };
      mockPrismaUser.update.mockResolvedValue(updatedUser);

      const result = await repository.update("user-123", updateData);

      expect(mockPrismaUser.update).toHaveBeenCalledWith({
        data: updateData,
        include: {
          department: true,
          location: true,
          role: true,
        },
        where: { id: "user-123" },
      });
      expect(result).toEqual(updatedUser);
    });
  });

  describe("delete", () => {
    it("should delete user", async () => {
      mockPrismaUser.delete.mockResolvedValue(mockUser);

      await repository.delete("user-123");

      expect(mockPrismaUser.delete).toHaveBeenCalledWith({
        where: { id: "user-123" },
      });
    });
  });
});
