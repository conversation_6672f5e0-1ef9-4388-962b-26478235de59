import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";
import { PaginationResult } from "@/common/utils";
import { Prisma } from "@/generated/prisma";

export interface UserData {
  departmentId: string
  email: string
  id: string
  locationId: string
  name: string
  roleId: string
}

export interface UserWithRelations extends UserData {
  department: {
    name: string
  }
  location: {
    city: string
    country: string
  }
  role: {
    code: string
  }
}

export interface CreateUserData {
  departmentId: string
  email: string
  locationId: string
  name: string
  roleId: string
}

export interface UpdateUserData {
  departmentId?: string
  email?: string
  locationId?: string
  name?: string
  roleId?: string
}

export interface UserFilters {
  departmentId?: string
  limit?: number
  locationId?: string
  page?: number
  roleCode?: string
  roleId?: string
  search?: string
}

@Injectable()
export class UserRepository {
  public constructor(private readonly prisma: PrismaService) {}

  public async create(userData: CreateUserData): Promise<UserWithRelations> {
    return this.prisma.user.create({
      data: userData,
      include: {
        department: true,
        location: true,
        role: true,
      },
    });
  }

  public async findAll(filters: UserFilters = {}): Promise<PaginationResult<UserWithRelations>> {
    const where: Prisma.UserWhereInput = {};
    const page = filters.page ?? 1;
    const limit = filters.limit ?? 10;
    const skip = (page - 1) * limit;

    if (filters.locationId) {
      where.locationId = filters.locationId;
    }

    if (filters.departmentId) {
      where.departmentId = filters.departmentId;
    }

    if (filters.roleId) {
      where.roleId = filters.roleId;
    }

    if (filters.roleCode) {
      where.role = {
        code: filters.roleCode,
      };
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: "insensitive" } },
        { email: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    const [data, total] = await Promise.all([
      this.prisma.user.findMany({
        include: {
          department: true,
          location: true,
          role: true,
        },
        orderBy: { name: "asc" },
        skip,
        take: limit,
        where,
      }),
      this.prisma.user.count({ where }),
    ]);

    return {
      data,
      limit,
      page,
      total,
    };
  }

  public async findById(id: string): Promise<UserWithRelations | null> {
    return this.prisma.user.findUnique({
      include: {
        department: true,
        location: true,
        role: true,
      },
      where: { id },
    });
  }

  public async findOneOrThrow(id: string): Promise<UserWithRelations> {
    const user = await this.findById(id);
    if (!user) {
      throw new Error(`User with ID '${id}' not found`);
    }
    return user;
  }

  public async findByEmail(email: string): Promise<UserData | null> {
    return this.prisma.user.findUnique({
      where: { email },
    });
  }

  public async update(id: string, userData: UpdateUserData): Promise<UserWithRelations> {
    return this.prisma.user.update({
      data: userData,
      include: {
        department: true,
        location: true,
        role: true,
      },
      where: { id },
    });
  }

  public async delete(id: string): Promise<void> {
    await this.prisma.user.delete({
      where: { id },
    });
  }
}
