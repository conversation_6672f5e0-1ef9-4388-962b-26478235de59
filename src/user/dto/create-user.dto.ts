import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString, IsUUID } from "class-validator";

export class CreateUserDto {
  @ApiProperty({ description: "Department ID", example: "921f82e3-e487-4d07-a585-6e86fb988fdc" })
  @IsUUID()
  public departmentId: string;

  @ApiProperty({ example: "<EMAIL>" })
  @IsEmail()
  public email: string;

  @ApiProperty({ description: "Location ID", example: "9c5ae51c-7ca3-4fa6-a8e3-c8df27782ae6" })
  @IsUUID()
  public locationId: string;

  @ApiProperty({ example: "Omar Press" })
  @IsNotEmpty()
  @IsString()
  public name: string;

  @ApiProperty({ description: "Role ID", example: "51c4f5ec-72f5-45b4-bec2-7e4be6a7b548" })
  @IsUUID()
  public roleId: string;
}
