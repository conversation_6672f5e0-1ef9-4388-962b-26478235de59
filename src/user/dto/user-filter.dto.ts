import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsOptional, IsString, IsUUID } from "class-validator";
import { PaginationQueryDto } from "../../common/dto/pagination-query.dto";
import { USER_ROLE } from "../../role/role.types";

export class UserFilterDto extends PaginationQueryDto {
  @ApiProperty({ description: "Filter by location ID", required: false })
  @IsOptional()
  @IsUUID()
  public locationId?: string;

  @ApiProperty({ description: "Filter by department ID", required: false })
  @IsOptional()
  @IsUUID()
  public departmentId?: string;

  @ApiProperty({ description: "Filter by role ID", required: false })
  @IsOptional()
  @IsUUID()
  public roleId?: string;

  @ApiProperty({
    description: "Filter by role code",
    enum: USER_ROLE,
    enumName: "USER_ROLE",
    required: false,
  })
  @IsOptional()
  @IsEnum(USER_ROLE)
  public roleCode?: USER_ROLE;

  @ApiProperty({ description: "Search by name or email", required: false })
  @IsOptional()
  @IsString()
  public search?: string;
}
