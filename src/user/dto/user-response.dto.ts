import { ApiProperty } from "@nestjs/swagger";

export class UserResponseDto {
  @ApiProperty({ example: "550e8400-e29b-41d4-a716-446655440000" })
  public id: string;

  @ApiProperty({ example: "Omar Press" })
  public name: string;

  @ApiProperty({ example: "<EMAIL>" })
  public email: string;

  @ApiProperty({ example: "Paris, France" })
  public location: string;

  @ApiProperty({ example: "Technology" })
  public department: string;

  @ApiProperty({ example: "ADMIN" })
  public role: string;
}
