import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiOAuth2,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { ErrorResponseDto } from "../common/dto";
import {
  CreateUserDto,
  PaginatedUserResponseDto,
  UpdateUserDto,
  UserFilterDto,
  UserResponseDto,
} from "./dto";
import { UserService } from "./user.service";
import { AuthGuard } from "@/auth/auth.guard";
import { RoleGuard, ROLES } from "@/auth/role.guard";
import { USER_ROLE } from "@/role/role.types";

@ApiBearerAuth()
@ApiOAuth2([process.env.AZURE_API_SCOPE ?? ""])
@UseGuards(AuthGuard, RoleGuard)
@ApiTags("users")
@Controller("users")
export class UserController {
  public constructor(private readonly userService: UserService) {}

  @ROLES(USER_ROLE.ADMIN)
  @Post()
  @ApiOperation({ summary: "Create a new user" })
  @ApiResponse({
    description: "User created successfully",
    status: HttpStatus.CREATED,
    type: UserResponseDto,
  })
  @ApiResponse({
    description: "Invalid input data",
    status: HttpStatus.BAD_REQUEST,
    type: ErrorResponseDto,
  })
  @ApiResponse({
    description: "User with email already exists",
    status: HttpStatus.CONFLICT,
    type: ErrorResponseDto,
  })
  public async create(@Body() createUserDto: CreateUserDto): Promise<UserResponseDto> {
    return this.userService.create(createUserDto);
  }

  @ROLES(USER_ROLE.ADMIN)
  @Get()
  @ApiOperation({ summary: "Get paginated list of users with filters" })
  @ApiResponse({
    description: "Users retrieved successfully",
    status: HttpStatus.OK,
    type: PaginatedUserResponseDto,
  })
  @ApiQuery({
    description: "Page number",
    example: 1,
    name: "page",
    required: false,
  })
  @ApiQuery({
    description: "Number of items per page",
    example: 10,
    name: "limit",
    required: false,
  })
  @ApiQuery({
    description: "Search by name or email",
    name: "search",
    required: false,
  })
  @ApiQuery({
    description: "Filter by role ID",
    name: "roleId",
    required: false,
  })
  @ApiQuery({
    description: "Filter by department ID",
    name: "departmentId",
    required: false,
  })
  @ApiQuery({
    description: "Filter by location ID",
    name: "locationId",
    required: false,
  })
  public async findAll(@Query() filters: UserFilterDto): Promise<PaginatedUserResponseDto> {
    return this.userService.findAll(filters);
  }

  @ROLES(USER_ROLE.ADMIN)
  @Get("filters")
  @ApiOperation({ summary: "Get filter options for user dropdowns" })
  @ApiResponse({
    description: "Filter options retrieved successfully",
    status: HttpStatus.OK,
  })
  public async getFilterOptions() {
    return this.userService.getFilterOptions();
  }

  @Get("profile")
  @ApiOperation({ summary: "Get current user profile" })
  @ApiResponse({
    description: "User profile retrieved successfully",
    status: HttpStatus.OK,
    type: UserResponseDto,
  })
  @ApiResponse({
    description: "User not found",
    status: HttpStatus.NOT_FOUND,
    type: ErrorResponseDto,
  })
  public async getCurrentUserProfile(@Req() request: { user: { oid: string } }): Promise<UserResponseDto> {
    const userId = request.user.oid;
    return this.userService.findOne(userId);
  }

  @ROLES(USER_ROLE.ADMIN)
  @Get(":id")
  @ApiOperation({ summary: "Get user by ID" })
  @ApiParam({
    description: "User ID",
    name: "id",
  })
  @ApiResponse({
    description: "User retrieved successfully",
    status: HttpStatus.OK,
    type: UserResponseDto,
  })
  @ApiResponse({
    description: "User not found",
    status: HttpStatus.NOT_FOUND,
    type: ErrorResponseDto,
  })
  public async findOne(@Param("id") id: string): Promise<UserResponseDto> {
    return this.userService.findOne(id);
  }

  @ROLES(USER_ROLE.ADMIN)
  @Patch(":id")
  @ApiOperation({ summary: "Update user by ID" })
  @ApiParam({
    description: "User ID",
    name: "id",
  })
  @ApiResponse({
    description: "User updated successfully",
    status: HttpStatus.OK,
    type: UserResponseDto,
  })
  @ApiResponse({
    description: "User not found",
    status: HttpStatus.NOT_FOUND,
    type: ErrorResponseDto,
  })
  @ApiResponse({
    description: "Invalid input data",
    status: HttpStatus.BAD_REQUEST,
    type: ErrorResponseDto,
  })
  public async update(
    @Param("id") id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<UserResponseDto> {
    return this.userService.update(id, updateUserDto);
  }

  @ROLES(USER_ROLE.ADMIN)
  @Delete(":id")
  @ApiOperation({ summary: "Delete user by ID" })
  @ApiParam({
    description: "User ID",
    name: "id",
  })
  @ApiResponse({
    description: "User deleted successfully",
    status: HttpStatus.NO_CONTENT,
  })
  @ApiResponse({
    description: "User not found",
    status: HttpStatus.NOT_FOUND,
    type: ErrorResponseDto,
  })
  public async remove(@Param("id") id: string): Promise<void> {
    return this.userService.remove(id);
  }
}
