import { NotFoundException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { DepartmentService } from "./department.service";
import { DepartmentRepository } from "./repositories/department.repository";

describe("DepartmentService", () => {
  let service: DepartmentService;

  const mockDepartment = { id: "1", name: "Test Department" };
  const mockDepartments = [mockDepartment];

  const mockFindAll = jest.fn().mockResolvedValue(mockDepartments);
  const mockFindOne = jest.fn().mockImplementation((id: string) => {
    if (id === "1") {
      return Promise.resolve(mockDepartment);
    }
    return Promise.resolve();
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DepartmentService,
        {
          provide: DepartmentRepository,
          useValue: {
            findAll: mockFindAll,
            findOne: mockFindOne,
          },
        },
      ],
    }).compile();

    service = module.get<DepartmentService>(DepartmentService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("findAll", () => {
    it("should return an array of departments", async () => {
      const departments = await service.findAll();
      expect(departments).toEqual([{ id: "1", name: "Test Department" }]);
      expect(mockFindAll).toHaveBeenCalled();
    });
  });

  describe("findOneOrThrow", () => {
    it("should return a single department if found", async () => {
      const department = await service.findOneOrThrow("1");
      expect(department).toEqual({ id: "1", name: "Test Department" });
      expect(mockFindOne).toHaveBeenCalledWith("1");
    });

    it("should throw a NotFoundException if the department is not found", async () => {
      await expect(service.findOneOrThrow("2")).rejects.toThrow(new NotFoundException("Department with ID '2' not found"));
    });
  });
});
