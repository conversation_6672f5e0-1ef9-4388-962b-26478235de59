import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "./prisma.service";

jest.mock("@/generated/prisma", () => {
  const mockPrismaClient = jest.fn().mockImplementation(() => ({
    $disconnect: jest.fn().mockImplementation(() => Promise.resolve()),
    $extends: jest.fn().mockReturnThis(),
  }));
  const mockExports = {};
  mockExports["PrismaClient"] = mockPrismaClient;
  return mockExports;
});

describe("PrismaService", () => {
  let service: PrismaService;

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [PrismaService],
    }).compile();

    service = module.get<PrismaService>(PrismaService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  it("should have required Prisma methods", () => {
    expect(service).toHaveProperty("$disconnect");
    expect(service).toHaveProperty("$extends");
  });
});
