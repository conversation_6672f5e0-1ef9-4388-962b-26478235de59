import { Logger, ValidationPipe } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import { FastifyAdapter, NestFastifyApplication } from "@nestjs/platform-fastify";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import { OpenTelemetryTransportV3 } from "@opentelemetry/winston-transport";
import { utilities as nestWinstonModuleUtilities, WinstonModule } from "nest-winston";
import * as winston from "winston";
import { AppModule } from "./app.module";
import "./instrumentation";

/**
 *
 */
async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter(),
    { logger: WinstonModule.createLogger({
      level: process.env.LOG_LEVEL ?? "info",
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.ms(),
            nestWinstonModuleUtilities.format.nestLike("Nest", {
              prettyPrint: true,
            }),
          ),
        }),
        new OpenTelemetryTransportV3(),
      ],
    }) }
  );
  app.enableCors({ methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"] });

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
    }),
  );

  const scopes = {};
  scopes[process.env.AZURE_API_SCOPE ?? ""] = "Access API as user";

  const config = new DocumentBuilder()
    .setTitle("Materiact API")
    .setDescription("API documentation for Materiact")
    .setVersion("1.0")
    .addOAuth2({
      flows: {
        implicit: {
          authorizationUrl: `https://login.microsoftonline.com/${process.env.AZURE_TENANT_ID}/oauth2/v2.0/authorize`,
          scopes,
        },
      },
      type: "oauth2",
    })
    .addBearerAuth()
    .build();
  const documentFactory = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup("api-docs", app, documentFactory, {
    swaggerOptions: {
      initOAuth: {
        clientId: process.env.AZURE_CLIENT_ID,
        scopes: [process.env.AZURE_API_SCOPE ?? ""],
      },
      oauth2RedirectUrl: process.env.AZURE_REDIRECT_URI,
      persistAuthorization: true,
    },
  });

  const host = process.env.HOST ?? "0.0.0.0";
  await app.listen(process.env.PORT ?? 3000, host);
  const url = await app.getUrl();
  const protocol = url.startsWith("https") ? "https" : "http";
  new Logger("Bootstrap").log(`Application is running on: ${protocol}://${host}:${process.env.PORT ?? 3000}`);
}

bootstrap();
