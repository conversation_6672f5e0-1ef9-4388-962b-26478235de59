import { UnauthorizedException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { Test, TestingModule } from "@nestjs/testing";
import * as jwt from "jsonwebtoken";
import { JwksClient } from "jwks-rsa";
import { AuthService } from "./auth.service";
import { PrismaService } from "@/prisma.service";

jest.mock("jsonwebtoken");
jest.mock("jwks-rsa");

describe("AuthService", () => {
  let service: AuthService;
  let prisma: PrismaService;
  let jwksClient: JwksClient;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === "AZURE_TENANT_ID") return "test-tenant-id";
              if (key === "AZURE_CLIENT_ID") return "test-client-id";
              return "";
            }),
          },
        },
        {
          provide: PrismaService,
          useValue: {
            user: {
              findUnique: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    prisma = module.get<PrismaService>(PrismaService);
    jwksClient = (service as unknown as { jwksClient: JwksClient }).jwksClient;
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("validateToken", () => {
    it("should throw an error if token is invalid", async () => {
      (jwt.decode as jest.Mock).mockReturnValue({});
      await expect(service.validateToken("invalid-token")).rejects.toThrow("Invalid token");
    });

    it("should throw UnauthorizedException if user not found", async () => {
      (jwt.decode as jest.Mock).mockReturnValue({ header: { kid: "test-kid" } });
      (jwksClient.getSigningKey as jest.Mock).mockResolvedValue({ getPublicKey: () => "test-key" });
      (jwt.verify as jest.Mock).mockReturnValue({ upn: "<EMAIL>" });
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({});

      await expect(service.validateToken("valid-token")).rejects.toThrow(UnauthorizedException);
    });

    it("should return user payload if token is valid", async () => {
      const user = { email: "<EMAIL>", id: "test-id", role: { code: "test-role" } };
      const payload = { upn: "<EMAIL>" };
      (jwt.decode as jest.Mock).mockReturnValue({ header: { kid: "test-kid" } });
      (jwksClient.getSigningKey as jest.Mock).mockResolvedValue({ getPublicKey: () => "test-key" });
      (jwt.verify as jest.Mock).mockReturnValue(payload);
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(user);

      const result = await service.validateToken("valid-token");

      expect(result.oid).toBe(user.id);
      expect(result.role).toBe(user.role.code);
    });

    it("should throw UnauthorizedException on jwt verify error", async () => {
      (jwt.decode as jest.Mock).mockReturnValue({ header: { kid: "test-kid" } });
      (jwksClient.getSigningKey as jest.Mock).mockResolvedValue({ getPublicKey: () => "test-key" });
      (jwt.verify as jest.Mock).mockImplementation(() => {
        throw new Error("jwt verify error");
      });

      await expect(service.validateToken("valid-token")).rejects.toThrow(UnauthorizedException);
    });
  });
});
