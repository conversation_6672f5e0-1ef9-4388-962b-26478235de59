import { Injectable, OnM<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/common";
import { context, SpanStatusCode, trace } from "@opentelemetry/api";
import { PrismaClient } from "@/generated/prisma";

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleDestroy {
  private readonly tracer = trace.getTracer("prisma");

  public constructor() {
    super();

    this.$extends({
      query: {
        $allOperations: async ({ args: databaseArguments, model, operation, query }: {
          args: unknown
          model?: string
          operation: string
          query: (databaseArguments: unknown) => Promise<unknown>
        }): Promise<unknown> => {
          const span = this.tracer.startSpan(`prisma:${model ?? "unknown"}.${operation}`, {
            attributes: {
              dbCollectionName: model,
              dbOperation: operation,
              prismaAction: operation,
              prismaModel: model,
            },
          });

          return context.with(trace.setSpan(context.active(), span), async () => {
            try {
              const before = Date.now();
              const result = await query(databaseArguments);
              const after = Date.now();

              span.setAttributes({
                dbDuration: after - before,
                prismaResultCount: Array.isArray(result) ? result.length : (result ? 1 : 0),
              });

              span.setStatus({ code: SpanStatusCode.OK });
              return result;
            }
            catch (error) {
              span.recordException(error as Error);
              span.setStatus({
                code: SpanStatusCode.ERROR,
                message: (error as Error).message,
              });
              throw error;
            }
            finally {
              span.end();
            }
          });
        },
      },
    }) as PrismaService;
  }

  public async onModuleDestroy() {
    await this.$disconnect();
  }
}
