import { Injectable, NotFoundException } from "@nestjs/common";
import { PaginationResult } from "@/common/utils";
import { Notification, NotificationType } from "@/generated/prisma";
import { PrismaService } from "@/prisma.service";

export interface NotificationFilters {
  isRead?: boolean
  limit?: number
  page?: number
  type?: NotificationType
  userId: string
}

@Injectable()
export class NotificationRepository {
  public constructor(private readonly prisma: PrismaService) {}

  public async findAll(filters: NotificationFilters): Promise<PaginationResult<Notification>> {
    const { isRead, limit = 10, page = 1, type, userId } = filters;
    const skip = (page - 1) * limit;

    const where: Record<string, unknown> = {
      userId,
    };

    if (type !== undefined) {
      where.type = type;
    }

    if (isRead !== undefined) {
      where.isRead = isRead;
    }

    const [data, total] = await Promise.all([
      this.prisma.notification.findMany({
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
        where,
      }),
      this.prisma.notification.count({ where }),
    ]);

    return {
      data,
      limit,
      page,
      total,
    };
  }

  public async findById(id: string): Promise<Notification | null> {
    return this.prisma.notification.findUnique({
      where: { id },
    });
  }

  public async findOneOrThrow(id: string): Promise<Notification> {
    const notification = await this.findById(id);
    if (!notification) {
      throw new NotFoundException(`Notification with ID '${id}' not found`);
    }
    return notification;
  }

  public async markAsRead(id: string): Promise<Notification> {
    return this.prisma.notification.update({
      data: {
        isRead: true,
        readAt: new Date(),
      },
      where: { id },
    });
  }

  public async markAsSeen(ids: string[], userId: string): Promise<void> {
    await this.prisma.notification.updateMany({
      data: {
        isSeen: true,
      },
      where: {
        id: { in: ids },
        isSeen: false,
        userId,
      },
    });
  }

  public async markManyAsRead(ids: string[], userId: string): Promise<Notification[]> {
    await this.prisma.notification.updateMany({
      data: {
        isRead: true,
        readAt: new Date(),
      },
      where: {
        id: { in: ids },
        isRead: false,
        userId,
      },
    });
    return this.prisma.notification.findMany({
      where: {
        id: { in: ids },
        userId,
      },
    });
  }

  public async getUnreadCount(userId: string): Promise<number> {
    return this.prisma.notification.count({
      where: {
        isRead: false,
        userId,
      },
    });
  }

  public async createMany({
    message,
    title,
    type,
    userIds,
  }: {
    message: string
    title: string
    type: NotificationType
    userIds: string[]
  }): Promise<Notification[]> {
    await this.prisma.notification.createMany({
      data: userIds.map(userId => ({
        message,
        title,
        type,
        userId,
      })),
    });
    return this.prisma.notification.findMany({
      orderBy: { createdAt: "desc" },
      take: userIds.length,
      where: {
        message,
        title,
        type,
        userId: { in: userIds },
      },
    });
  }
}
