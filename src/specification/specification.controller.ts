import {
  Controller,
  Get,
  HttpStatus,
  Query,
  UseGuards,
} from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiOAuth2,
  <PERSON>pi<PERSON>peration,
  <PERSON>piQuery,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { AuthGuard } from "../auth/auth.guard";
import { PaginatedSpecificationResponseDto } from "./dto";
import { SpecificationService } from "./specification.service";

@ApiBearerAuth()
@ApiOAuth2([process.env.AZURE_API_SCOPE ?? ""])
@UseGuards(AuthGuard)
@ApiTags("specifications")
@Controller("specifications")
export class SpecificationController {
  public constructor(private readonly specificationService: SpecificationService) {}

  @Get()
  @ApiOperation({ summary: "Get paginated list of specification" })
  @ApiQuery({ description: "Search by label or property", name: "search", required: false })
  @ApiQuery({ description: "Page number", name: "page", required: false })
  @ApiQuery({ description: "Items per page", name: "limit", required: false })
  @ApiResponse({
    description: "Specification criteria retrieved successfully",
    status: HttpStatus.OK,
    type: PaginatedSpecificationResponseDto,
  })
  public async findAll(
    @Query("search") search?: string,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
  ): Promise<PaginatedSpecificationResponseDto> {
    return this.specificationService.findAll({
      limit,
      page,
      search,
    });
  }
}
