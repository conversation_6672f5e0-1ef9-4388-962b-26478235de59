import { Injectable } from "@nestjs/common";
import { calculatePaginationOffset } from "../../common/utils";
import { PrismaService } from "../../prisma.service";
import type { Prisma, Specification } from "@/generated/prisma";

export interface CreateSpecificationData {
  label: string
  property: string
  unit?: string
}

export interface UpdateSpecificationData {
  label?: string
  property?: string
  unit?: string
}

export interface SpecificationFilters {
  limit?: number
  page?: number
  search?: string
}

export interface PaginatedSpecificationResult {
  data: Specification[]
  limit: number
  page: number
  total: number
}

@Injectable()
export class SpecificationRepository {
  public constructor(private readonly prisma: PrismaService) {}

  public async findAll(filters: SpecificationFilters): Promise<PaginatedSpecificationResult> {
    const { limit = 10, page = 1, ...filterParameters } = filters;
    const offset = calculatePaginationOffset(page, limit);

    const where: Prisma.SpecificationWhereInput = {};

    if (filterParameters.search) {
      where.OR = [
        { label: { contains: filterParameters.search, mode: "insensitive" } },
        { property: { contains: filterParameters.search, mode: "insensitive" } },
      ];
    }

    const [data, total] = await Promise.all([
      this.prisma.specification.findMany({
        orderBy: {
          updatedAt: "desc",
        },
        skip: offset,
        take: limit,
        where,
      }),
      this.prisma.specification.count({ where }),
    ]);

    return {
      data,
      limit,
      page,
      total,
    };
  }

  public async findById(id: string): Promise<Specification | null> {
    return this.prisma.specification.findUnique({
      where: { id },
    });
  }
}
