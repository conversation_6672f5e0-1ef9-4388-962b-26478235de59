import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../../prisma.service";
import { SpecificationRepository } from "./specification.repository";
import { SpecificationType } from "@/generated/prisma";

describe("SpecificationRepository", () => {
  let repository: SpecificationRepository;
  let prismaService: PrismaService;

  const mockPrismaService = {
    specification: {
      count: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SpecificationRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<SpecificationRepository>(SpecificationRepository);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("findAll", () => {
    const mockSpecifications = [
      {
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        id: "spec-1",
        label: "Tensile Strength",
        property: "tensileStrength",
        type: SpecificationType.RANGE,
        unit: "MPa",
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      },
      {
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        id: "spec-2",
        label: "Melt Flow Index",
        property: "meltFlowIndex",
        type: SpecificationType.RANGE,
        unit: "g/10min",
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      },
    ];

    it("should return paginated specifications with default pagination", async () => {
      mockPrismaService.specification.findMany.mockResolvedValue(mockSpecifications);
      mockPrismaService.specification.count.mockResolvedValue(2);

      const filters = {};
      const result = await repository.findAll(filters);

      expect(mockPrismaService.specification.findMany).toHaveBeenCalledWith({
        orderBy: {
          updatedAt: "desc",
        },
        skip: 0,
        take: 10,
        where: {},
      });

      expect(mockPrismaService.specification.count).toHaveBeenCalledWith({
        where: {},
      });

      expect(result).toEqual({
        data: mockSpecifications,
        limit: 10,
        page: 1,
        total: 2,
      });
    });

    it("should return paginated specifications with custom pagination", async () => {
      const limitedSpecs = [mockSpecifications[0]];
      mockPrismaService.specification.findMany.mockResolvedValue(limitedSpecs);
      mockPrismaService.specification.count.mockResolvedValue(2);

      const filters = {
        limit: 1,
        page: 2,
      };

      const result = await repository.findAll(filters);

      expect(mockPrismaService.specification.findMany).toHaveBeenCalledWith({
        orderBy: {
          updatedAt: "desc",
        },
        skip: 1,
        take: 1,
        where: {},
      });

      expect(mockPrismaService.specification.count).toHaveBeenCalledWith({
        where: {},
      });

      expect(result).toEqual({
        data: limitedSpecs,
        limit: 1,
        page: 2,
        total: 2,
      });
    });

    it("should filter specifications by search term in label", async () => {
      const filteredSpecs = [mockSpecifications[0]];
      mockPrismaService.specification.findMany.mockResolvedValue(filteredSpecs);
      mockPrismaService.specification.count.mockResolvedValue(1);

      const filters = {
        limit: 10,
        page: 1,
        search: "Tensile",
      };

      const result = await repository.findAll(filters);

      expect(mockPrismaService.specification.findMany).toHaveBeenCalledWith({
        orderBy: {
          updatedAt: "desc",
        },
        skip: 0,
        take: 10,
        where: {
          OR: [
            { label: { contains: "Tensile", mode: "insensitive" } },
            { property: { contains: "Tensile", mode: "insensitive" } },
          ],
        },
      });

      expect(mockPrismaService.specification.count).toHaveBeenCalledWith({
        where: {
          OR: [
            { label: { contains: "Tensile", mode: "insensitive" } },
            { property: { contains: "Tensile", mode: "insensitive" } },
          ],
        },
      });

      expect(result).toEqual({
        data: filteredSpecs,
        limit: 10,
        page: 1,
        total: 1,
      });
    });

    it("should filter specifications by search term in property", async () => {
      const filteredSpecs = [mockSpecifications[1]];
      mockPrismaService.specification.findMany.mockResolvedValue(filteredSpecs);
      mockPrismaService.specification.count.mockResolvedValue(1);

      const filters = {
        limit: 10,
        page: 1,
        search: "melt",
      };

      const result = await repository.findAll(filters);

      expect(mockPrismaService.specification.findMany).toHaveBeenCalledWith({
        orderBy: {
          updatedAt: "desc",
        },
        skip: 0,
        take: 10,
        where: {
          OR: [
            { label: { contains: "melt", mode: "insensitive" } },
            { property: { contains: "melt", mode: "insensitive" } },
          ],
        },
      });

      expect(result.data).toEqual(filteredSpecs);
      expect(result.total).toBe(1);
    });

    it("should return empty results when no specifications match search", async () => {
      mockPrismaService.specification.findMany.mockResolvedValue([]);
      mockPrismaService.specification.count.mockResolvedValue(0);

      const filters = {
        limit: 10,
        page: 1,
        search: "nonexistent",
      };

      const result = await repository.findAll(filters);

      expect(mockPrismaService.specification.findMany).toHaveBeenCalledWith({
        orderBy: {
          updatedAt: "desc",
        },
        skip: 0,
        take: 10,
        where: {
          OR: [
            { label: { contains: "nonexistent", mode: "insensitive" } },
            { property: { contains: "nonexistent", mode: "insensitive" } },
          ],
        },
      });

      expect(result).toEqual({
        data: [],
        limit: 10,
        page: 1,
        total: 0,
      });
    });

    it("should handle case-insensitive search", async () => {
      mockPrismaService.specification.findMany.mockResolvedValue(mockSpecifications);
      mockPrismaService.specification.count.mockResolvedValue(2);

      const filters = {
        limit: 10,
        page: 1,
        search: "TENSILE",
      };

      await repository.findAll(filters);

      expect(mockPrismaService.specification.findMany).toHaveBeenCalledWith({
        orderBy: {
          updatedAt: "desc",
        },
        skip: 0,
        take: 10,
        where: {
          OR: [
            { label: { contains: "TENSILE", mode: "insensitive" } },
            { property: { contains: "TENSILE", mode: "insensitive" } },
          ],
        },
      });
    });

    it("should handle special characters in search query", async () => {
      mockPrismaService.specification.findMany.mockResolvedValue([]);
      mockPrismaService.specification.count.mockResolvedValue(0);

      const filters = {
        limit: 10,
        page: 1,
        search: "test@#$%^&*()",
      };

      await repository.findAll(filters);

      expect(mockPrismaService.specification.findMany).toHaveBeenCalledWith({
        orderBy: {
          updatedAt: "desc",
        },
        skip: 0,
        take: 10,
        where: {
          OR: [
            { label: { contains: "test@#$%^&*()", mode: "insensitive" } },
            { property: { contains: "test@#$%^&*()", mode: "insensitive" } },
          ],
        },
      });
    });

    it("should handle very large page numbers", async () => {
      mockPrismaService.specification.findMany.mockResolvedValue([]);
      mockPrismaService.specification.count.mockResolvedValue(100);

      const filters = {
        limit: 10,
        page: 999,
      };

      await repository.findAll(filters);

      expect(mockPrismaService.specification.findMany).toHaveBeenCalledWith({
        orderBy: {
          updatedAt: "desc",
        },
        skip: 9980,
        take: 10,
        where: {},
      });
    });

    it("should handle very large limit values", async () => {
      mockPrismaService.specification.findMany.mockResolvedValue(mockSpecifications);
      mockPrismaService.specification.count.mockResolvedValue(2);

      const filters = {
        limit: 1000,
        page: 1,
      };

      await repository.findAll(filters);

      expect(mockPrismaService.specification.findMany).toHaveBeenCalledWith({
        orderBy: {
          updatedAt: "desc",
        },
        skip: 0,
        take: 1000,
        where: {},
      });
    });

    it("should maintain correct order by updatedAt desc", async () => {
      const orderedSpecs = [
        {
          ...mockSpecifications[1],
          updatedAt: new Date("2024-01-03T00:00:00.000Z"),
        },
        {
          ...mockSpecifications[0],
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
      ];

      mockPrismaService.specification.findMany.mockResolvedValue(orderedSpecs);
      mockPrismaService.specification.count.mockResolvedValue(2);

      const result = await repository.findAll({});

      expect(mockPrismaService.specification.findMany).toHaveBeenCalledWith({
        orderBy: {
          updatedAt: "desc",
        },
        skip: 0,
        take: 10,
        where: {},
      });

      expect(result.data).toEqual(orderedSpecs);
    });
  });

  describe("findById", () => {
    const mockSpecification = {
      createdAt: new Date("2024-01-01T00:00:00.000Z"),
      id: "spec-1",
      label: "Tensile Strength",
      property: "tensileStrength",
      type: SpecificationType.RANGE,
      unit: "MPa",
      updatedAt: new Date("2024-01-02T00:00:00.000Z"),
    };

    it("should return a specification when found", async () => {
      mockPrismaService.specification.findUnique.mockResolvedValue(mockSpecification);

      const result = await repository.findById("spec-1");

      expect(mockPrismaService.specification.findUnique).toHaveBeenCalledWith({
        where: { id: "spec-1" },
      });

      expect(result).toEqual(mockSpecification);
    });

    it("should return null when specification is not found", async () => {
      mockPrismaService.specification.findUnique.mockImplementation(() => Promise.resolve());

      const result = await repository.findById("non-existent-id");

      expect(mockPrismaService.specification.findUnique).toHaveBeenCalledWith({
        where: { id: "non-existent-id" },
      });

      expect(result).toBeUndefined();
    });

    it("should handle empty string id", async () => {
      mockPrismaService.specification.findUnique.mockImplementation(() => Promise.resolve());

      const result = await repository.findById("");

      expect(mockPrismaService.specification.findUnique).toHaveBeenCalledWith({
        where: { id: "" },
      });

      expect(result).toBeUndefined();
    });

    it("should handle UUID format ids", async () => {
      const uuidId = "123e4567-e89b-12d3-a456-************";
      mockPrismaService.specification.findUnique.mockResolvedValue(mockSpecification);

      await repository.findById(uuidId);

      expect(mockPrismaService.specification.findUnique).toHaveBeenCalledWith({
        where: { id: uuidId },
      });
    });

    it("should handle specifications with null unit values", async () => {
      const specWithNullUnit = {
        ...mockSpecification,
        type: SpecificationType.TEXT,
        unit: undefined,
      };

      mockPrismaService.specification.findUnique.mockResolvedValue(specWithNullUnit);

      const result = await repository.findById("spec-1");

      expect(result).toEqual(specWithNullUnit);
      expect(result?.unit).toBeUndefined();
    });

    it("should handle different specification types", async () => {
      const textSpecification = {
        ...mockSpecification,
        id: "spec-text",
        label: "Color",
        property: "color",
        type: SpecificationType.TEXT,
        unit: undefined,
      };

      mockPrismaService.specification.findUnique.mockResolvedValue(textSpecification);

      const result = await repository.findById("spec-text");

      expect(result?.type).toBe(SpecificationType.TEXT);
      expect(result?.property).toBe("color");
    });
  });

  describe("repository initialization", () => {
    it("should be defined", () => {
      expect(repository).toBeDefined();
    });

    it("should have prisma service injected", () => {
      expect(prismaService).toBeDefined();
    });
  });
});
