import { Test, TestingModule } from "@nestjs/testing";
import { SpecificationRepository } from "./repositories/specification.repository";
import { SpecificationService } from "./specification.service";
import { SpecificationType } from "@/generated/prisma";

describe("SpecificationService", () => {
  let service: SpecificationService;
  let repository: SpecificationRepository;

  const mockSpecificationRepository = {
    findAll: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SpecificationService,
        {
          provide: SpecificationRepository,
          useValue: mockSpecificationRepository,
        },
      ],
    }).compile();

    service = module.get<SpecificationService>(SpecificationService);
    repository = module.get<SpecificationRepository>(SpecificationRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("findAll", () => {
    it("should return paginated specifications with search filter", async () => {
      const mockSpecifications = [
        {
          createdAt: new Date("2024-01-01T00:00:00.000Z"),
          id: "spec-1",
          label: "Tensile Strength",
          property: "tensileStrength",
          type: SpecificationType.RANGE,
          unit: "MPa",
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
        {
          createdAt: new Date("2024-01-01T00:00:00.000Z"),
          id: "spec-2",
          label: "Melt Flow Index",
          property: "meltFlowIndex",
          type: SpecificationType.RANGE,
          unit: "g/10min",
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
      ];

      const mockRepositoryResult = {
        data: mockSpecifications,
        limit: 10,
        page: 1,
        total: 2,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        limit: 10,
        page: 1,
        search: "tensile",
      };

      const result = await service.findAll(filters);

      expect(mockSpecificationRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: "tensile",
      });

      expect(result.data).toHaveLength(2);
      expect(result.data[0]).toEqual({
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        id: "spec-1",
        label: "Tensile Strength",
        property: "tensileStrength",
        type: SpecificationType.RANGE,
        unit: "MPa",
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      });

      expect(result.meta).toEqual({
        currentPage: 1,
        from: 1,
        lastPage: 1,
        perPage: 10,
        to: 2,
        total: 2,
      });
    });

    it("should return paginated specifications without search filter", async () => {
      const mockSpecifications = [
        {
          createdAt: new Date("2024-01-01T00:00:00.000Z"),
          id: "spec-1",
          label: "Density",
          property: "density",
          type: SpecificationType.RANGE,
          unit: "g/cm³",
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
      ];

      const mockRepositoryResult = {
        data: mockSpecifications,
        limit: 10,
        page: 1,
        total: 1,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        limit: 10,
        page: 1,
      };

      const result = await service.findAll(filters);

      expect(mockSpecificationRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: undefined,
      });

      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toEqual({
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        id: "spec-1",
        label: "Density",
        property: "density",
        type: SpecificationType.RANGE,
        unit: "g/cm³",
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      });

      expect(result.meta).toEqual({
        currentPage: 1,
        from: 1,
        lastPage: 1,
        perPage: 10,
        to: 1,
        total: 1,
      });
    });

    it("should handle empty results", async () => {
      const mockRepositoryResult = {
        data: [],
        limit: 10,
        page: 1,
        total: 0,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        limit: 10,
        page: 1,
        search: "nonexistent",
      };

      const result = await service.findAll(filters);

      expect(mockSpecificationRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: "nonexistent",
      });

      expect(result.data).toHaveLength(0);
      expect(result.meta).toEqual({
        currentPage: 1,
        from: 0,
        lastPage: 0,
        perPage: 10,
        to: 0,
        total: 0,
      });
    });

    it("should normalize pagination parameters", async () => {
      const mockRepositoryResult = {
        data: [],
        limit: 10,
        page: 1,
        total: 0,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        search: "test",
      };

      await service.findAll(filters);

      expect(mockSpecificationRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: "test",
      });
    });

    it("should handle pagination with different page sizes", async () => {
      const mockSpecifications = Array.from({ length: 25 }, (_, index) => ({
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        id: `spec-${index + 1}`,
        label: `Property ${index + 1}`,
        property: `property${index + 1}`,
        type: SpecificationType.RANGE,
        unit: "unit",
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      }));

      const mockRepositoryResult = {
        data: mockSpecifications.slice(0, 5),
        limit: 5,
        page: 1,
        total: 25,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        limit: 5,
        page: 1,
      };

      const result = await service.findAll(filters);

      expect(mockSpecificationRepository.findAll).toHaveBeenCalledWith({
        limit: 5,
        page: 1,
        search: undefined,
      });

      expect(result.data).toHaveLength(5);
      expect(result.meta).toEqual({
        currentPage: 1,
        from: 1,
        lastPage: 5,
        perPage: 5,
        to: 5,
        total: 25,
      });
    });

    it("should handle specifications with null unit values", async () => {
      const mockSpecifications = [
        {
          createdAt: new Date("2024-01-01T00:00:00.000Z"),
          id: "spec-1",
          label: "Color",
          property: "color",
          type: SpecificationType.TEXT,
          unit: undefined,
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
      ];

      const mockRepositoryResult = {
        data: mockSpecifications,
        limit: 10,
        page: 1,
        total: 1,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        limit: 10,
        page: 1,
      };

      const result = await service.findAll(filters);

      expect(result.data[0]).toEqual({
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        id: "spec-1",
        label: "Color",
        property: "color",
        type: SpecificationType.TEXT,
        unit: undefined,
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      });
    });

    it("should handle different specification types", async () => {
      const mockSpecifications = [
        {
          createdAt: new Date("2024-01-01T00:00:00.000Z"),
          id: "spec-range",
          label: "Temperature",
          property: "temperature",
          type: SpecificationType.RANGE,
          unit: "°C",
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
        {
          createdAt: new Date("2024-01-01T00:00:00.000Z"),
          id: "spec-text",
          label: "Material Type",
          property: "material",
          type: SpecificationType.TEXT,
          unit: undefined,
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
      ];

      const mockRepositoryResult = {
        data: mockSpecifications,
        limit: 10,
        page: 1,
        total: 2,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const result = await service.findAll({ limit: 10, page: 1 });

      expect(result.data).toHaveLength(2);
      expect(result.data[0].type).toBe(SpecificationType.RANGE);
      expect(result.data[1].type).toBe(SpecificationType.TEXT);
    });

    it("should handle special characters in search query", async () => {
      const mockRepositoryResult = {
        data: [],
        limit: 10,
        page: 1,
        total: 0,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        limit: 10,
        page: 1,
        search: "test@#$%^&*()_+",
      };

      const result = await service.findAll(filters);

      expect(mockSpecificationRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: "test@#$%^&*()_+",
      });

      expect(result.data).toHaveLength(0);
    });

    it("should handle unicode characters in search query", async () => {
      const mockRepositoryResult = {
        data: [],
        limit: 10,
        page: 1,
        total: 0,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        limit: 10,
        page: 1,
        search: "测试данные🔬",
      };

      const result = await service.findAll(filters);

      expect(mockSpecificationRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: "测试данные🔬",
      });

      expect(result.data).toHaveLength(0);
    });
  });

  describe("error handling", () => {
    it("should propagate repository errors for findAll", async () => {
      const repositoryError = new Error("Database connection failed");
      mockSpecificationRepository.findAll.mockRejectedValue(repositoryError);

      const filters = { limit: 10, page: 1 };

      await expect(service.findAll(filters)).rejects.toThrow("Database connection failed");
      expect(mockSpecificationRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: undefined,
      });
    });

    it("should handle timeout errors gracefully", async () => {
      const timeoutError = new Error("Query timeout");
      mockSpecificationRepository.findAll.mockRejectedValue(timeoutError);

      const filters = { limit: 10, page: 1 };

      await expect(service.findAll(filters)).rejects.toThrow("Query timeout");
    });
  });

  describe("service initialization", () => {
    it("should be defined", () => {
      expect(service).toBeDefined();
    });

    it("should have repository injected", () => {
      expect(repository).toBeDefined();
    });
  });

  describe("mapToResponseDto", () => {
    it("should correctly map specification to response DTO", async () => {
      const mockSpecification = {
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        id: "test-spec-id",
        label: "Test Property",
        property: "testProperty",
        type: SpecificationType.RANGE,
        unit: "test-unit",
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      };

      const mockRepositoryResult = {
        data: [mockSpecification],
        limit: 10,
        page: 1,
        total: 1,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const result = await service.findAll({ limit: 10, page: 1 });

      expect(result.data[0]).toEqual({
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        id: "test-spec-id",
        label: "Test Property",
        property: "testProperty",
        type: SpecificationType.RANGE,
        unit: "test-unit",
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      });
    });
  });
});
