import { Test, TestingModule } from "@nestjs/testing";
import { AuthGuard } from "../auth/auth.guard";
import { SpecificationController } from "./specification.controller";
import { SpecificationService } from "./specification.service";
import { SpecificationType } from "@/generated/prisma";

describe("SpecificationController", () => {
  let controller: SpecificationController;

  const mockSpecificationService = {
    findAll: jest.fn(),
  };

  const mockAuthGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SpecificationController],
      providers: [
        {
          provide: SpecificationService,
          useValue: mockSpecificationService,
        },
      ],
    })
      .overrideGuard(AuthGuard)
      .useValue(mockAuthGuard)
      .compile();

    controller = module.get<SpecificationController>(SpecificationController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });

  describe("findAll", () => {
    it("should return paginated specifications with all query parameters", async () => {
      const expectedResult = {
        data: [
          {
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            id: "spec-1",
            label: "Tensile Strength",
            property: "tensileStrength",
            type: SpecificationType.RANGE,
            unit: "MPa",
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
          {
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            id: "spec-2",
            label: "Melt Flow Index",
            property: "meltFlowIndex",
            type: SpecificationType.RANGE,
            unit: "g/10min",
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 2,
          total: 2,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll("strength", 1, 10);

      expect(mockSpecificationService.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: "strength",
      });
      expect(result).toEqual(expectedResult);
    });

    it("should return paginated specifications with search parameter only", async () => {
      const expectedResult = {
        data: [
          {
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            id: "spec-1",
            label: "Density",
            property: "density",
            type: SpecificationType.RANGE,
            unit: "g/cm³",
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll("density");

      expect(mockSpecificationService.findAll).toHaveBeenCalledWith({
        limit: undefined,
        page: undefined,
        search: "density",
      });
      expect(result).toEqual(expectedResult);
    });

    it("should return paginated specifications with pagination parameters only", async () => {
      const expectedResult = {
        data: [
          {
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            id: "spec-1",
            label: "Temperature",
            property: "temperature",
            type: SpecificationType.RANGE,
            unit: "°C",
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          currentPage: 2,
          from: 6,
          lastPage: 3,
          perPage: 5,
          to: 10,
          total: 15,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll(undefined, 2, 5);

      expect(mockSpecificationService.findAll).toHaveBeenCalledWith({
        limit: 5,
        page: 2,
        search: undefined,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should return paginated specifications with no query parameters", async () => {
      const expectedResult = {
        data: [
          {
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            id: "spec-1",
            label: "Viscosity",
            property: "viscosity",
            type: SpecificationType.RANGE,
            unit: "Pa⋅s",
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
          {
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            id: "spec-2",
            label: "Color",
            property: "color",
            type: SpecificationType.TEXT,
            unit: undefined,
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 2,
          total: 2,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll();

      expect(mockSpecificationService.findAll).toHaveBeenCalledWith({
        limit: undefined,
        page: undefined,
        search: undefined,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle empty search results", async () => {
      const expectedResult = {
        data: [],
        meta: {
          currentPage: 1,
          from: 0,
          lastPage: 0,
          perPage: 10,
          to: 0,
          total: 0,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll("nonexistent", 1, 10);

      expect(mockSpecificationService.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: "nonexistent",
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle case-insensitive search", async () => {
      const expectedResult = {
        data: [
          {
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            id: "spec-1",
            label: "Tensile Strength",
            property: "tensileStrength",
            type: SpecificationType.RANGE,
            unit: "MPa",
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll("TENSILE", 1, 10);

      expect(mockSpecificationService.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: "TENSILE",
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle specifications with different types", async () => {
      const expectedResult = {
        data: [
          {
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            id: "spec-range",
            label: "Pressure",
            property: "pressure",
            type: SpecificationType.RANGE,
            unit: "bar",
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
          {
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            id: "spec-text",
            label: "Grade",
            property: "grade",
            type: SpecificationType.TEXT,
            unit: undefined,
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 2,
          total: 2,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll(undefined, 1, 10);

      expect(mockSpecificationService.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: undefined,
      });
      expect(result).toEqual(expectedResult);
      expect(result.data[0].type).toBe(SpecificationType.RANGE);
      expect(result.data[1].type).toBe(SpecificationType.TEXT);
    });

    it("should handle large page numbers", async () => {
      const expectedResult = {
        data: [],
        meta: {
          currentPage: 100,
          from: 991,
          lastPage: 5,
          perPage: 10,
          to: 1000,
          total: 50,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll(undefined, 100, 10);

      expect(mockSpecificationService.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 100,
        search: undefined,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle specifications with special characters in search", async () => {
      const expectedResult = {
        data: [
          {
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            id: "spec-1",
            label: "Elastic Modulus (E-Modulus)",
            property: "elasticModulus",
            type: SpecificationType.RANGE,
            unit: "GPa",
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll("E-Modulus", 1, 10);

      expect(mockSpecificationService.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: "E-Modulus",
      });
      expect(result).toEqual(expectedResult);
    });
  });
});
