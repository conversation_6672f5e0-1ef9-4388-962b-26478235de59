import { ApiProperty } from "@nestjs/swagger";
import { PaginationMetaDto } from "../../common/dto/pagination-meta.dto";
import { SpecificationResponseDto } from "./specification-response.dto";

export class PaginatedSpecificationResponseDto {
  @ApiProperty({ description: "Array of specification objects", type: [SpecificationResponseDto] })
  public data: SpecificationResponseDto[];

  @ApiProperty({ description: "Pagination metadata", type: PaginationMetaDto })
  public meta: PaginationMetaDto;
}
