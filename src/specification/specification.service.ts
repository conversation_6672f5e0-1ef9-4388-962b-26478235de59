import { Injectable } from "@nestjs/common";
import { createPaginatedResponse, normalizePaginationParameters } from "../common/utils";
import { PaginatedSpecificationResponseDto, SpecificationResponseDto } from "./dto";
import { SpecificationRepository } from "./repositories/specification.repository";
import type { Specification } from "@/generated/prisma";

@Injectable()
export class SpecificationService {
  public constructor(
    private readonly specificationRepository: SpecificationRepository,
  ) {}

  public async findAll(filters: {
    limit?: number
    page?: number
    search?: string
  }): Promise<PaginatedSpecificationResponseDto> {
    const { limit, page } = normalizePaginationParameters(filters.page, filters.limit);

    const result = await this.specificationRepository.findAll({
      limit,
      page,
      search: filters.search,
    });

    return createPaginatedResponse({
      data: result.data.map(spec => this.mapToResponseDto(spec)),
      limit: result.limit,
      page: result.page,
      total: result.total,
    });
  }

  private mapToResponseDto(specification: Specification): SpecificationResponseDto {
    return {
      createdAt: specification.createdAt,
      id: specification.id,
      label: specification.label,
      property: specification.property,
      type: specification.type,
      unit: specification.unit,
      updatedAt: specification.updatedAt,
    };
  }
}
