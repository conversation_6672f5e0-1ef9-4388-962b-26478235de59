import { CRITERIA_OPERATOR } from "@/common/dto/criteria-operator.dto";

interface FilterCriterion {
  maxValue?: unknown
  minValue?: unknown
  operator: CRITERIA_OPERATOR
  propertyName: string
  value?: unknown
}

type FilterCondition = Record<string, unknown>;

interface FilterResult {
  ["AND"]?: Record<string, FilterCondition>[]
}

export const filterConditionBuilder = {
  buildCondition(criterion: FilterCriterion): FilterCondition | undefined {
    const { maxValue, minValue, operator, value } = criterion;

    switch (operator) {
      case CRITERIA_OPERATOR.GREATER_THAN_OR_EQUAL: {
        return { gte: value };
      }
      case CRITERIA_OPERATOR.GREATER_THAN: {
        return { gt: value };
      }
      case CRITERIA_OPERATOR.LESS_THAN_OR_EQUAL: {
        return { lte: value };
      }
      case CRITERIA_OPERATOR.LESS_THAN: {
        return { lt: value };
      }
      case CRITERIA_OPERATOR.EQUAL: {
        return { equals: value };
      }
      case CRITERIA_OPERATOR.BETWEEN: {
        return { gte: minValue, lte: maxValue };
      }
      default: {
        return undefined;
      }
    }
  },

  buildGenericFilters(criteria: FilterCriterion[]): FilterResult {
    const andConditions: Record<string, FilterCondition>[] = [];

    for (const criterion of criteria) {
      const condition = filterConditionBuilder.buildCondition(criterion);
      if (condition) {
        andConditions.push({ [criterion.propertyName]: condition });
      }
    }

    return andConditions.length > 0 ? { ["AND"]: andConditions } : {};
  },
};
