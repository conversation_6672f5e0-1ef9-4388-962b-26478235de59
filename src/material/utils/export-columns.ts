import { MaterialFamily } from "@/generated/prisma";

type PropertyType = "Text" | "Number" | "Date";

export interface ColumnDefinition {
  label: string
  name: string
  type: PropertyType
}
/**
 * Get the valid properties with their types for a material family
 * @param family - The material family
 * @returns Array of header with types
 */
export function getColumnDefinitions(family: MaterialFamily): ColumnDefinition[] {
  const familyMaps: Record<MaterialFamily, ColumnDefinition[]> = {
    [MaterialFamily.ADDITIVES]: [
      { label: "Status", name: "status", type: "Text" },
      { label: "Supplier", name: "supplier", type: "Text" },
      { label: "code_anonym", name: "anonymizationCode", type: "Text" },
      { label: "origin", name: "origin", type: "Text" },
    ],
    [MaterialFamily.ELASTOMERS]: [
      { label: "Status", name: "status", type: "Text" },
      { label: "name", name: "type", type: "Text" },
      { label: "Supplier", name: "supplier", type: "Text" },
      { label: "density", name: "density", type: "Number" },
      { label: "mfi190216", name: "mfi190216", type: "Number" },
      { label: "code_anonym_elasto", name: "codeanonymElasto", type: "Text" },
      { label: "n_izod_23", name: "nIzod23", type: "Number" },
      { label: "flex_modulus", name: "flexModulus", type: "Number" },
      { label: "trac_modulus_100", name: "tracModulus100", type: "Number" },
      { label: "elong_at_break", name: "elongAtBreak", type: "Number" },
      { label: "mfi230216", name: "mfi230216", type: "Number" },
      { label: "melting_point", name: "meltingPoint", type: "Number" },
      { label: "hdt_b", name: "hdtB", type: "Number" },
      { label: "hdt_a", name: "hdtA", type: "Number" },
      { label: "shore_a", name: "shoreA", type: "Number" },
      { label: "shore_d", name: "shoreD", type: "Number" },
      { label: "origin", name: "origin", type: "Text" },
    ],
    [MaterialFamily.FILLERS]: [
      { label: "Status", name: "status", type: "Text" },
      { label: "code anonymisatiton", name: "codeanonymFiller", type: "Text" },
      { label: "reference", name: "reference", type: "Text" },
      { label: "Supplier", name: "supplier", type: "Text" },
      { label: "bet", name: "bet", type: "Number" },
      { label: "d50", name: "d50", type: "Number" },
      { label: "d98_d95", name: "d95d05", type: "Number" },
      { label: "whiteness", name: "whiteness", type: "Number" },
      { label: "form", name: "form", type: "Text" },
      { label: "origin", name: "origin", type: "Text" },
    ],
    [MaterialFamily.POLYMERS]: [
      { label: "Status", name: "status", type: "Text" },
      { label: "Product Reference", name: "reference", type: "Text" },
      { label: "Supplier", name: "supplier", type: "Text" },
      { label: "Batch Number", name: "supplierBatchNumber", type: "Text" },
      { label: "Region", name: "origin", type: "Text" },
      { label: "Result Update Date", name: "resultUpdateDate", type: "Date" },
      { label: "Validation Engineering + Feedstock or use in Compounds", name: "validationEngineeringAndFeedstockOrUseInCompounds", type: "Date" },
      { label: "TDS", name: "tds", type: "Text" },
      { label: "Price EXW €/t", name: "priceExw", type: "Number" },
      { label: "Price EXW €/t", name: "priceExwDate", type: "Date" },
      { label: "Comment", name: "comment", type: "Text" },
      { label: "MFI - Norme", name: "mfiNorme", type: "Text" },
      { label: "MFI - Test Conditions", name: "mfiTestConditions", type: "Text" },
      { label: "MFI - Av", name: "mfiAv", type: "Number" },
      { label: "MFI - St dv", name: "mfiStdDv", type: "Number" },
      { label: "Density - Norme", name: "densityNorme", type: "Text" },
      { label: "Density - Av", name: "densityAv", type: "Number" },
      { label: "Density - St dv", name: "densityStdDv", type: "Number" },
      { label: "Tensile Modulus - Norme", name: "tensileModulusNorme", type: "Text" },
      { label: "Tensile Modulus - Conditions", name: "tensileModulusConditions", type: "Text" },
      { label: "Tensile Modulus - Av", name: "tensileModulusAv", type: "Number" },
      { label: "Tensile Modulus - St dv", name: "tensileModulusStdDv", type: "Number" },
      { label: "Flexural Modulus - Norme", name: "flexuralModulusNorme", type: "Text" },
      { label: "Flexural Modulus - Av", name: "flexuralModulusAv", type: "Number" },
      { label: "Flexural Modulus - St dev", name: "flexuralModulusStdDev", type: "Number" },
      { label: "Flexural Stress @fc - Av", name: "flexuralStressFcAv", type: "Number" },
      { label: "Flexural Stress @fc - St dev", name: "flexuralStressFcStdDev", type: "Number" },
      { label: "Stress @Break - Norme", name: "stressBreakNorme", type: "Text" },
      { label: "Stress @Break - Av", name: "stressBreakAv", type: "Number" },
      { label: "Stress @Break - St dv", name: "stressBreakStdDv", type: "Number" },
      { label: "Stress @Yield - Av", name: "stressYieldAv", type: "Number" },
      { label: "Stress @Yield - St dv", name: "stressYieldStdDv", type: "Number" },
      { label: "Yield Strain - Av", name: "yieldStrainAv", type: "Number" },
      { label: "Yield strain - St dv", name: "yieldStrainStdDv", type: "Number" },
      { label: "Strain @break - Norme", name: "strainBreakNorme", type: "Text" },
      { label: "Strain @break - Av", name: "strainBreakAv", type: "Number" },
      { label: "Strain @break - St dv", name: "strainBreakStdDv", type: "Number" },
      { label: "Nominal Strain @break - Av", name: "nominalStrainBreakAv", type: "Number" },
      { label: "Nominal Strain @break - St dv", name: "nominalStrainBreakStdDv", type: "Number" },
      { label: "Notched IZOD Impact - Norme", name: "notchedIzodNorme", type: "Text" },
      { label: "Notched IZOD Impact - Av", name: "notchedIzodAv", type: "Number" },
      { label: "Notched IZOD Impact - St dv", name: "notchedIzodStdDv", type: "Number" },
      { label: "Notched IZOD Impact - Type of failure", name: "notchedIzodFailureType", type: "Text" },
      { label: "Unnotched IZOD Impact - Norme", name: "unnotchedIzodNorme", type: "Text" },
      { label: "Unnotched IZOD Impact - Av", name: "unnotchedIzodAv", type: "Number" },
      { label: "Unnotched IZOD Impact - St dv", name: "unnotchedIzodStdDv", type: "Number" },
      { label: "Unnotched IZOD Impact - type of failure", name: "unnotchedIzodFailureType", type: "Text" },
      { label: "HDT B - Norme", name: "hdtBNorme", type: "Text" },
      { label: "HDT B - Av", name: "hdtBAv", type: "Number" },
      { label: "HDT B - St dv", name: "hdtBStdDv", type: "Number" },
      { label: "CMA 23°C- Norm", name: "cma23Norm", type: "Text" },
      { label: "CMA 23°C - v (m/s)", name: "cma23V", type: "Number" },
      { label: "CMA 23°C - Nb samples", name: "cma23NbSamples", type: "Number" },
      { label: "CMA 23°C - Mean Break type", name: "cma23MeanBreakType", type: "Text" },
      { label: "CMA 23°C - Energy at Force max - Av", name: "cma23EnergyForceMaxAv", type: "Number" },
      { label: "CMA 23°C - Energy at Force max - St dev", name: "cma23EnergyForceMaxStdDev", type: "Number" },
      { label: "CMA 23°C - Energy at Puncture - Av", name: "cma23EnergyPunctureAv", type: "Number" },
      { label: "CMA 23°C - Energy at Puncture - St dev", name: "cma23EnergyPunctureStdDev", type: "Number" },
      { label: "CMA 0°C - Norm", name: "cma0Norm", type: "Text" },
      { label: "CMA 0°C - v (m/s)", name: "cma0V", type: "Number" },
      { label: "CMA 0°C - Nb samples", name: "cma0NbSamples", type: "Number" },
      { label: "CMA 0°C - Mean Break type", name: "cma0MeanBreakType", type: "Text" },
      { label: "CMA 0°C - Energy at Force max - Av", name: "cma0EnergyForceMaxAv", type: "Number" },
      { label: "CMA 0°C - Energy at Force max - St dev", name: "cma0EnergyForceMaxStdDev", type: "Number" },
      { label: "CMA 0°C - Energy at Puncture - Av", name: "cma0EnergyPunctureAv", type: "Number" },
      { label: "CMA 0°C - Energy at Puncture - St dev", name: "cma0EnergyPunctureStdDev", type: "Number" },
      { label: "CMA -10°C - Norm", name: "cma10Norm", type: "Text" },
      { label: "CMA -10°C - v (m/s)", name: "cma10V", type: "Number" },
      { label: "CMA -10°C - Nb samples", name: "cma10NbSamples", type: "Number" },
      { label: "CMA -10°C - Mean Break type", name: "cma10MeanBreakType", type: "Text" },
      { label: "CMA -10°C - Energy at Force max - Av", name: "cma10EnergyForceMaxAv", type: "Number" },
      { label: "CMA -10°C - Energy at Force max - St dev", name: "cma10EnergyForceMaxStdDev", type: "Number" },
      { label: "CMA -10°C - Energy at Puncture - Av", name: "cma10EnergyPunctureAv", type: "Number" },
      { label: "CMA -10°C - Energy at Puncture - St dev", name: "cma10EnergyPunctureStdDev", type: "Number" },
      { label: "CMA -20°C - Norm", name: "cma20Norm", type: "Text" },
      { label: "CMA -20°C - v (m/s)", name: "cma20V", type: "Number" },
      { label: "CMA -20°C - Nb samples", name: "cma20NbSamples", type: "Number" },
      { label: "CMA -20°C - Mean Break type", name: "cma20MeanBreakType", type: "Text" },
      { label: "CMA -20°C - Energy at Force max - Av", name: "cma20EnergyForceMaxAv", type: "Number" },
      { label: "CMA -20°C - Energy at Force max - St dev", name: "cma20EnergyForceMaxStdDev", type: "Number" },
      { label: "CMA -20°C - Energy at Puncture - Av", name: "cma20EnergyPunctureAv", type: "Number" },
      { label: "CMA -20°C - Energy at Puncture - St dev", name: "cma20EnergyPunctureStdDev", type: "Number" },
      { label: "CMA -30°C - Norm", name: "cma30Norm", type: "Text" },
      { label: "CMA -30°C - v (m/s)", name: "cma30V", type: "Number" },
      { label: "CMA -30°C - Nb samples", name: "cma30NbSamples", type: "Number" },
      { label: "CMA -30°C - Mean Break type", name: "cma30MeanBreakType", type: "Text" },
      { label: "CMA -30°C - Energy at Force max - Av", name: "cma30EnergyForceMaxAv", type: "Number" },
      { label: "CMA -30°C - Energy at Force max - St dev", name: "cma30EnergyForceMaxStdDev", type: "Number" },
      { label: "CMA -30°C - Energy at Puncture - Av", name: "cma30EnergyPunctureAv", type: "Number" },
      { label: "CMA -30°C - Energy at Puncture - St dev", name: "cma30EnergyPunctureStdDev", type: "Number" },
    ],
    [MaterialFamily.RECYCLE_POLYMERS]: [
      { label: "Status", name: "status", type: "Text" },
      { label: "Product Reference", name: "reference", type: "Text" },
      { label: "Supplier", name: "supplier", type: "Text" },
      { label: "Batch Number", name: "supplierBatchNumber", type: "Text" },
      { label: "Region", name: "origin", type: "Text" },
      { label: "Result Update Date", name: "resultUpdateDate", type: "Date" },
      { label: "Validation Engineering + Feedstock or use in Compounds", name: "validationEngineeringAndFeedstockOrUseInCompounds", type: "Date" },
      { label: "Color", name: "color", type: "Text" },
      { label: "PIR/PCR/ELV", name: "pirPcrElv", type: "Text" },
      { label: "Waste Details ", name: "wasteDetails", type: "Text" },
      { label: "Material Form ", name: "materialForm", type: "Text" },
      { label: "TDS", name: "tds", type: "Text" },
      { label: "Product Volumes (kt/y)", name: "productVolumesKtY", type: "Number" },
      { label: "Volumes available for M'ACT (kt/y)", name: "volumesAvailableForMactKtY", type: "Number" },
      { label: "Price EXW €/t", name: "priceExw", type: "Number" },
      { label: "Price EXW €/t", name: "priceExwDate", type: "Date" },
      { label: "Certificates, Reach/RoHS by products", name: "certificatesReachRoHS", type: "Text" },
      { label: "End of waste status ", name: "endOfWasteStatus", type: "Text" },
      { label: "Comment", name: "comment", type: "Text" },
      { label: "Filtration location", name: "filtrationLocation", type: "Text" },
      { label: "System Filtration", name: "systemFiltration", type: "Text" },
      { label: "Filtration size", name: "filtrationSize", type: "Text" },
      { label: "Quantity of filtered remaining", name: "quantityFilteredRemaining", type: "Number" },
      { label: "D22 - Nb filters used", name: "d22NbFiltersUsed", type: "Number" },
      { label: "D22 Nb filtre/kg feedstock", name: "d22NbFilterPerKgFeedstock", type: "Number" },
      { label: "D32 - quantity of scrap", name: "d32QuantityScrap", type: "Number" },
      { label: "D32 Nb filtre/kg feedstock", name: "d32NbFilterPerKgFeedstock", type: "Number" },
      { label: "Level of Pollution", name: "levelOfPollution", type: "Text" },
      { label: "Venting", name: "venting", type: "Text" },
      { label: "Trial Date", name: "trialDate", type: "Date" },
      { label: "Vacuum pressure", name: "vacuumPressure", type: "Text" },
      { label: "Screw speed", name: "screwSpeed", type: "Text" },
      { label: "Screw profile", name: "screwProfile", type: "Text" },
      { label: "MFI - Norme", name: "mfiNorme", type: "Text" },
      { label: "MFI - test conditions", name: "mfiTestConditions", type: "Text" },
      { label: "MFI - Av", name: "mfiAv", type: "Number" },
      { label: "MFI - St dv", name: "mfiStdDv", type: "Number" },
      { label: "Ash content - Norme", name: "ashContentNorme", type: "Text" },
      { label: "Ash content - Av", name: "ashContentAv", type: "Number" },
      { label: "Ash content - St dv", name: "ashContentStdDv", type: "Number" },
      { label: "Density - Norme", name: "densityNorme", type: "Text" },
      { label: "Density - Av", name: "densityAv", type: "Number" },
      { label: "Density - St dv", name: "densityStdDv", type: "Number" },
      { label: "Tensile Modulus - Norme", name: "tensileModulusNorme", type: "Text" },
      { label: "Tensile Modulus - Conditions", name: "tensileModulusConditions", type: "Text" },
      { label: "Tensile Modulus - Av", name: "tensileModulusAv", type: "Number" },
      { label: "Tensile Modulus - St dv", name: "tensileModulusStdDv", type: "Number" },
      { label: "Flexural Modulus - Norme", name: "flexuralModulusNorme", type: "Text" },
      { label: "Flexural Modulus - Av", name: "flexuralModulusAv", type: "Number" },
      { label: "Flexural Modulus - St dev", name: "flexuralModulusStdDev", type: "Number" },
      { label: "Flexural Stress @fc - Av", name: "flexuralStressFcAv", type: "Number" },
      { label: "Flexural Stress @fc - St dev", name: "flexuralStressFcStdDev", type: "Number" },
      { label: "Stress @Break - Norme", name: "stressBreakNorme", type: "Text" },
      { label: "Stress @Break - Av", name: "stressBreakAv", type: "Number" },
      { label: "Stress @Break - St dv", name: "stressBreakStdDv", type: "Number" },
      { label: "Stress @Yield - Av", name: "stressYieldAv", type: "Number" },
      { label: "Stress @Yield - St dv", name: "stressYieldStdDv", type: "Number" },
      { label: "Yield Strain - Av", name: "yieldStrainAv", type: "Number" },
      { label: "Yield strain - St dv", name: "yieldStrainStdDv", type: "Number" },
      { label: "Strain @break - Norme", name: "strainBreakNorme", type: "Text" },
      { label: "Strain @break - Av", name: "strainBreakAv", type: "Number" },
      { label: "Strain @break - St dv", name: "strainBreakStdDv", type: "Number" },
      { label: "Nominal Strain @break - Av", name: "nominalStrainBreakAv", type: "Number" },
      { label: "Nominal Strain @break - St dv", name: "nominalStrainBreakStdDv", type: "Number" },
      { label: "Notched IZOD Impact - Norme", name: "notchedIzodNorme", type: "Text" },
      { label: "Notched IZOD Impact - Av", name: "notchedIzodAv", type: "Number" },
      { label: "Notched IZOD Impact - St dv", name: "notchedIzodStdDv", type: "Number" },
      { label: "Notched IZOD Impact - Type of failure", name: "notchedIzodFailureType", type: "Text" },
      { label: "Unnotched IZOD Impact - Norme", name: "unnotchedIzodNorme", type: "Text" },
      { label: "Unnotched IZOD Impact - Av", name: "unnotchedIzodAv", type: "Number" },
      { label: "Unnotched IZOD Impact - St dv", name: "unnotchedIzodStdDv", type: "Number" },
      { label: "Unnotched IZOD Impact - type of failure", name: "unnotchedIzodFailureType", type: "Text" },
      { label: "HDT B - Norme", name: "hdtBNorme", type: "Text" },
      { label: "HDT B - Av", name: "hdtBAv", type: "Number" },
      { label: "HDT B - St dv", name: "hdtBStdDv", type: "Number" },
      { label: "Odor - Norme", name: "odorNorme", type: "Text" },
      { label: "Odor - note 1", name: "odorNote1", type: "Text" },
      { label: "Odor - note 2", name: "odorNote2", type: "Text" },
      { label: "Odor - note 3", name: "odorNote3", type: "Text" },
      { label: "Odor - Av", name: "odorAv", type: "Number" },
      { label: "Odor - Stdv", name: "odorStdv", type: "Number" },
      { label: "VOC/FOG - Norme", name: "vocFogNorme", type: "Text" },
      { label: "VOC", name: "voc", type: "Number" },
      { label: "VOC2", name: "voc2", type: "Number" },
      { label: "FOG", name: "fog", type: "Number" },
      { label: "L", name: "l", type: "Number" },
      { label: "a", name: "a", type: "Number" },
      { label: "b", name: "b", type: "Number" },
      { label: "CMA 23°C- Norm", name: "cma23Norm", type: "Text" },
      { label: "CMA 23°C - v (m/s)", name: "cma23V", type: "Number" },
      { label: "CMA 23°C - Nb samples", name: "cma23NbSamples", type: "Number" },
      { label: "CMA 23°C - Mean Break type", name: "cma23MeanBreakType", type: "Text" },
      { label: "CMA 23°C - Energy at Force max - Av", name: "cma23EnergyForceMaxAv", type: "Number" },
      { label: "CMA 23°C - Energy at Force max - St dev", name: "cma23EnergyForceMaxStdDev", type: "Number" },
      { label: "CMA 23°C - Energy at Puncture - Av", name: "cma23EnergyPunctureAv", type: "Number" },
      { label: "CMA 23°C - Energy at Puncture - St dev", name: "cma23EnergyPunctureStdDev", type: "Number" },
      { label: "CMA 0°C - Norm", name: "cma0Norm", type: "Text" },
      { label: "CMA 0°C - v (m/s)", name: "cma0V", type: "Number" },
      { label: "CMA 0°C - Nb samples", name: "cma0NbSamples", type: "Number" },
      { label: "CMA 0°C - Mean Break type", name: "cma0MeanBreakType", type: "Text" },
      { label: "CMA 0°C - Energy at Force max - Av", name: "cma0EnergyForceMaxAv", type: "Number" },
      { label: "CMA 0°C - Energy at Force max - St dev", name: "cma0EnergyForceMaxStdDev", type: "Number" },
      { label: "CMA 0°C - Energy at Puncture - Av", name: "cma0EnergyPunctureAv", type: "Number" },
      { label: "CMA 0°C - Energy at Puncture - St dev", name: "cma0EnergyPunctureStdDev", type: "Number" },
      { label: "CMA -10°C - Norm", name: "cma10Norm", type: "Text" },
      { label: "CMA -10°C - v (m/s)", name: "cma10V", type: "Number" },
      { label: "CMA -10°C - Nb samples", name: "cma10NbSamples", type: "Number" },
      { label: "CMA -10°C - Mean Break type", name: "cma10MeanBreakType", type: "Text" },
      { label: "CMA -10°C - Energy at Force max - Av", name: "cma10EnergyForceMaxAv", type: "Number" },
      { label: "CMA -10°C - Energy at Force max - St dev", name: "cma10EnergyForceMaxStdDev", type: "Number" },
      { label: "CMA -10°C - Energy at Puncture - Av", name: "cma10EnergyPunctureAv", type: "Number" },
      { label: "CMA -10°C - Energy at Puncture - St dev", name: "cma10EnergyPunctureStdDev", type: "Number" },
      { label: "CMA -20°C - Norm", name: "cma20Norm", type: "Text" },
      { label: "CMA -20°C - v (m/s)", name: "cma20V", type: "Number" },
      { label: "CMA -20°C - Nb samples", name: "cma20NbSamples", type: "Number" },
      { label: "CMA -20°C - Mean Break type", name: "cma20MeanBreakType", type: "Text" },
      { label: "CMA -20°C - Energy at Force max - Av", name: "cma20EnergyForceMaxAv", type: "Number" },
      { label: "CMA -20°C - Energy at Force max - St dev", name: "cma20EnergyForceMaxStdDev", type: "Number" },
      { label: "CMA -20°C - Energy at Puncture - Av", name: "cma20EnergyPunctureAv", type: "Number" },
      { label: "CMA -20°C - Energy at Puncture - St dev", name: "cma20EnergyPunctureStdDev", type: "Number" },
      { label: "CMA -30°C - Norm", name: "cma30Norm", type: "Text" },
      { label: "CMA -30°C - v (m/s)", name: "cma30V", type: "Number" },
      { label: "CMA -30°C - Nb samples", name: "cma30NbSamples", type: "Number" },
      { label: "CMA -30°C - Mean Break type", name: "cma30MeanBreakType", type: "Text" },
      { label: "CMA -30°C - Energy at Force max - Av", name: "cma30EnergyForceMaxAv", type: "Number" },
      { label: "CMA -30°C - Energy at Force max - St dev", name: "cma30EnergyForceMaxStdDev", type: "Number" },
      { label: "CMA -30°C - Energy at Puncture - Av", name: "cma30EnergyPunctureAv", type: "Number" },
      { label: "CMA -30°C - Energy at Puncture - St dev", name: "cma30EnergyPunctureStdDev", type: "Number" },
      { label: "1 Shredding", name: "shredding", type: "Text" },
      { label: "2 Over - Band", name: "overBand", type: "Text" },
      { label: "3 Washing", name: "washing", type: "Text" },
      { label: "4 Drying", name: "drying", type: "Text" },
      { label: "5 Manual Sorting", name: "manualSorting", type: "Text" },
      { label: "6 Densimetric Sorting", name: "densimetricSorting", type: "Text" },
      { label: "7 Optical Sorting", name: "opticalSorting", type: "Text" },
      { label: "8 Flotation", name: "flotation", type: "Text" },
      { label: "9 Color Sorting", name: "colorSorting", type: "Text" },
      { label: "10 Triboelectric Sorting", name: "triboelectricSorting", type: "Text" },
      { label: "11 Aeraulic Sorting", name: "aeraulicSorting", type: "Text" },
      { label: "12 Vacuum Treatment Along Extrusion Line", name: "vacuumTreatmentAlongExtrusionLine", type: "Text" },
      { label: "13 Decantation", name: "decantation", type: "Text" },
      { label: "14 Filtration or Size", name: "filtrationOrSize", type: "Text" },
      { label: "15 Degasification", name: "degasification", type: "Text" },
      { label: "16 Homogenization", name: "homogenization", type: "Text" },
      { label: "17 Extrusion Line", name: "extrusionLine", type: "Text" },
      { label: "18 Lot Size (T)", name: "lotSizeT", type: "Text" },
      { label: "Chronology", name: "chronology", type: "Text" },
    ],
  };

  return familyMaps[family] || [];
}
