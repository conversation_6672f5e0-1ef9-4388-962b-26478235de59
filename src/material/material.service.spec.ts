import { ForbiddenException, NotFoundException } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { Test, TestingModule } from "@nestjs/testing";
import { USER_ROLE } from "../role/role.types";
import { UserService } from "../user/user.service";
import { BatchUpdateMaterialRequestDto } from "./dto";
import { MATERIAL_FILTER_COLUMN } from "./enum/material-filter-column.enum";
import { MaterialStatusChangedEvent } from "./events";
import { MaterialService, MaterialWithFamilyData } from "./material.service";
import { MaterialFilterRepository } from "./repositories/material-filter.repository";
import { MaterialRepository } from "./repositories/material.repository";
import { CriteriaOperator } from "@/common/dto/criteria-operator.dto";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";
import { SupplierService } from "@/supplier/supplier.service";

describe("MaterialService", () => {
  let service: MaterialService;

  const mockMaterialRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    findDistinctOrigins: jest.fn(),
    findWithFamilyFieldFilters: jest.fn(),
    update: jest.fn(),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
  };

  const mockUserService = {
    findAll: jest.fn(),
  };

  const mockSupplierService = {
    findOneByName: jest.fn(),
  };

  const mockMaterialFilterRepository = {
    findDistinctColumnValues: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    mockUserService.findAll.mockResolvedValue({ data: [] });

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MaterialService,
        {
          provide: MaterialRepository,
          useValue: mockMaterialRepository,
        },
        {
          provide: MaterialFilterRepository,
          useValue: mockMaterialFilterRepository,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: SupplierService,
          useValue: mockSupplierService,
        },
      ],
    }).compile();

    service = module.get<MaterialService>(MaterialService);
  });

  describe("searchMaterials", () => {
    it("should search materials with search parameter and handle empty results", async () => {
      const mockMaterials = [
        {
          family: "POLYMERS",
          id: "material-1",
          origin: "Europe",
          reference: "MAT-001",
          status: MaterialStatus.AVAILABLE,
          supplier: { name: "Test Supplier" },
          supplierBatchNumber: "BATCH-123",
          type: "Polymer",
        },
      ];

      const mockRepositoryResult = {
        data: mockMaterials,
        limit: 10,
        page: 1,
        total: 1,
      };

      mockMaterialRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const searchRequest = {
        limit: 10,
        page: 1,
        search: "polymer",
      };

      const result = await service.searchMaterials(searchRequest);

      expect(mockMaterialRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: "polymer",
      });

      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toEqual({
        family: "POLYMERS",
        id: "material-1",
        origin: "Europe",
        reference: "MAT-001",
        status: MaterialStatus.AVAILABLE,
        supplier: "Test Supplier",
        supplierBatchNumber: "BATCH-123",
        type: "Polymer",
      });
      expect(result.meta.total).toBe(1);
    });

    it("should filter materials by status for engineers", async () => {
      const mockRepositoryResult = {
        data: [],
        limit: 10,
        page: 1,
        total: 0,
      };

      mockMaterialRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const searchRequest = {
        limit: 10,
        page: 1,
        search: "polymer",
        userRole: USER_ROLE.ENGINEERS,
      };

      await service.searchMaterials(searchRequest);

      expect(mockMaterialRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        page: 1,
        search: "polymer",
        status: MaterialStatus.AVAILABLE,
      });
    });
  });

  describe("getMaterialOrigins", () => {
    it("should return distinct material origins and handle edge cases", async () => {
      const mockOrigins = [
        { origin: "Asia" },
        { origin: "EUROPE" },
        { origin: undefined },
        { origin: "" },
        { origin: "north america" },
      ];

      mockMaterialRepository.findDistinctOrigins.mockResolvedValue(mockOrigins);

      const result = await service.getMaterialOrigins();

      expect(mockMaterialRepository.findDistinctOrigins).toHaveBeenCalledWith();
      expect(result).toEqual(["Asia", "Europe", "North America"]);
      expect(result).toHaveLength(3);
    });

    it("should return empty array when no origins exist", async () => {
      mockMaterialRepository.findDistinctOrigins.mockResolvedValue([]);

      const result = await service.getMaterialOrigins();

      expect(result).toEqual([]);
    });
  });

  describe("searchMaterialsWithCriteria", () => {
    it("should search materials with family criteria", async () => {
      const mockMaterials = [
        {
          family: MaterialFamily.POLYMERS,
          id: "material-1",
          origin: "Europe",
          polymer: {
            densityAv: 0.98,
            id: "polymer-1",
            mfiAv: 15.2,
            tensileModulusAv: 3000,
          },
          reference: "MAT-001",
          status: MaterialStatus.AVAILABLE,
          supplier: { name: "Test Supplier" },
          supplierBatchNumber: "BATCH-123",
          type: "High-Performance Polymer",
        },
      ];

      const mockRepositoryResult = {
        data: mockMaterials,
        limit: 10,
        page: 1,
        total: 1,
      };

      mockMaterialRepository.findWithFamilyFieldFilters.mockResolvedValue(mockRepositoryResult);

      const searchRequest = {
        family: MaterialFamily.POLYMERS,
        familyCriteria: [
          {
            operator: CriteriaOperator.GREATER_THAN_OR_EQUAL,
            propertyName: "mfiAv",
            value: 10,
          },
        ],
        userRole: USER_ROLE.ADMIN,
      };

      const result = await service.searchMaterialsWithCriteria(searchRequest);

      expect(mockMaterialRepository.findWithFamilyFieldFilters).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        familyFilters: [
          {
            operator: CriteriaOperator.GREATER_THAN_OR_EQUAL,
            propertyName: "mfiAv",
            value: 10,
          },
        ],
        limit: 10,
        page: 1,
        userRole: USER_ROLE.ADMIN,
      });

      expect(result.data).toHaveLength(1);
      expect(result.data[0].familyData).toEqual({
        densityAv: 0.98,
        id: "polymer-1",
        mfiAv: 15.2,
        tensileModulusAv: 3000,
      });
    });

    it("should handle FEEDSTOCK_RECYCLING_MEMBERS role", async () => {
      const mockRepositoryResult = {
        data: [],
        limit: 10,
        page: 1,
        total: 0,
      };

      mockMaterialRepository.findWithFamilyFieldFilters.mockResolvedValue(mockRepositoryResult);

      const searchRequest = {
        family: MaterialFamily.RECYCLE_POLYMERS,
        userRole: USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS,
      };

      await service.searchMaterialsWithCriteria(searchRequest);

      expect(mockMaterialRepository.findWithFamilyFieldFilters).toHaveBeenCalledWith({
        family: "RECYCLE_POLYMERS",
        familyFilters: undefined,
        limit: 10,
        page: 1,
        userRole: USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS,
      });
    });

    it("should handle pagination and filters", async () => {
      const mockRepositoryResult = {
        data: [],
        limit: 5,
        page: 3,
        total: 25,
      };

      mockMaterialRepository.findWithFamilyFieldFilters.mockResolvedValue(mockRepositoryResult);

      const searchRequest = {
        family: MaterialFamily.POLYMERS,
        limit: 5,
        origin: ["Europe", "Asia"],
        page: 3,
        status: [MaterialStatus.AVAILABLE],
        userRole: USER_ROLE.ADMIN,
      };

      const result = await service.searchMaterialsWithCriteria(searchRequest);

      expect(mockMaterialRepository.findWithFamilyFieldFilters).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        familyFilters: undefined,
        limit: 5,
        origin: ["Europe", "Asia"],
        page: 3,
        status: [MaterialStatus.AVAILABLE],
        userRole: USER_ROLE.ADMIN,
      });

      expect(result.meta).toEqual({
        currentPage: 3,
        from: 11,
        lastPage: 5,
        perPage: 5,
        to: 15,
        total: 25,
      });
    });
  });

  describe("updateMaterial", () => {
    it("should remove materialId from familyData if present", async () => {
      const customMaterialId = "mat-with-family-id";
      const customExistingMaterial = {
        family: MaterialFamily.POLYMERS,
        id: customMaterialId,
        origin: "Europe",
        reference: "MAT-010",
        status: MaterialStatus.AVAILABLE,
        supplier: { name: "Test Supplier" },
        supplierBatchNumber: "BATCH-010",
        type: "Polymer",
      };
      const updateDto = {
        family: MaterialFamily.POLYMERS,
        familyData: { materialId: "should-be-removed", mfiAv: 2.5 },
        supplierBatchNumber: "BATCH-011",
      };
      const updatedMaterial = { ...customExistingMaterial, supplierBatchNumber: "BATCH-011" };

      mockMaterialRepository.findById.mockResolvedValue(customExistingMaterial);
      mockMaterialRepository.update.mockResolvedValue(updatedMaterial);

      await service.updateMaterial(customMaterialId, { ...updateDto }, USER_ROLE.ADMIN);

      expect(mockMaterialRepository.update).toHaveBeenCalledWith(
        customMaterialId,
        expect.objectContaining({
          family: MaterialFamily.POLYMERS,
          familyData: { mfiAv: 2.5 },
          supplierBatchNumber: "BATCH-011",
        })
      );
    });

    const materialId = "test-id";
    const existingMaterial = {
      family: MaterialFamily.POLYMERS,
      id: materialId,
      origin: "Europe",
      reference: "MAT-001",
      status: MaterialStatus.AVAILABLE,
      supplier: { name: "Test Supplier" },
      supplierBatchNumber: "BATCH-123",
      type: "Original Material",
    };

    it("should update a material successfully", async () => {
      const updateDto = {
        family: MaterialFamily.POLYMERS,
        status: MaterialStatus.ARCHIVE,
        supplierBatchNumber: "UPDATED-BATCH-123",
        type: "Updated Material",
      };

      const updatedMaterial = { ...existingMaterial, ...updateDto };

      mockMaterialRepository.findById.mockResolvedValue(existingMaterial);
      mockMaterialRepository.update.mockResolvedValue(updatedMaterial);
      mockUserService.findAll.mockResolvedValue({ data: [{ id: "user-1" }] });

      const result = await service.updateMaterial(materialId, updateDto, USER_ROLE.ADMIN);

      expect(mockMaterialRepository.findById).toHaveBeenCalledWith(materialId);
      expect(mockMaterialRepository.update).toHaveBeenCalledWith(materialId, {
        family: MaterialFamily.POLYMERS,
        status: MaterialStatus.ARCHIVE,
        supplierBatchNumber: "UPDATED-BATCH-123",
        type: "Updated Material",
      });
      expect(result.type).toBe("Updated Material");
      expect(result.status).toBe(MaterialStatus.ARCHIVE);
    });

    it("should throw NotFoundException when material does not exist", async () => {
      const updateDto = {
        family: MaterialFamily.POLYMERS,
        supplierBatchNumber: "BATCH-123",
        type: "Updated Material",
      };

      mockMaterialRepository.findById.mockImplementation(() => Promise.resolve());

      await expect(service.updateMaterial(materialId, updateDto, USER_ROLE.ADMIN)).rejects.toThrow(
        NotFoundException,
      );
      expect(mockMaterialRepository.update).not.toHaveBeenCalled();
    });

    it("should update material with familyData", async () => {
      const updateDto = {
        family: MaterialFamily.POLYMERS,
        familyData: { mfiAv: 2.5 },
        supplierBatchNumber: "BATCH-456",
      };

      const updatedMaterial = { ...existingMaterial, supplierBatchNumber: "BATCH-456" };

      mockMaterialRepository.findById.mockResolvedValue(existingMaterial);
      mockMaterialRepository.update.mockResolvedValue(updatedMaterial);

      const result = await service.updateMaterial(materialId, updateDto, USER_ROLE.ADMIN);

      expect(mockMaterialRepository.update).toHaveBeenCalledWith(materialId, {
        family: MaterialFamily.POLYMERS,
        familyData: { mfiAv: 2.5 },
        supplierBatchNumber: "BATCH-456",
      });
      expect(result.supplierBatchNumber).toBe("BATCH-456");
    });

    it("should update supplierId and remove supplier if supplier is provided", async () => {
      const updateDto = {
        family: MaterialFamily.POLYMERS,
        status: MaterialStatus.AVAILABLE,
        supplier: "SupplierX",
        supplierBatchNumber: "BATCH-999",
        type: "Updated Material",
      };
      const updatedMaterial = { ...existingMaterial, ...updateDto, supplierId: "sup-123" };
      mockMaterialRepository.findById.mockResolvedValue(existingMaterial);
      mockSupplierService.findOneByName.mockResolvedValue({ id: "sup-123", name: "SupplierX" });
      mockMaterialRepository.update.mockResolvedValue(updatedMaterial);
      mockUserService.findAll.mockResolvedValue({ data: [{ id: "user-1" }] });

      const result = await service.updateMaterial(materialId, { ...updateDto }, USER_ROLE.ADMIN);

      expect(mockSupplierService.findOneByName).toHaveBeenCalledWith("SupplierX");
      expect(mockMaterialRepository.update).toHaveBeenCalledWith(
        materialId,
        expect.objectContaining({
          family: MaterialFamily.POLYMERS,
          status: MaterialStatus.AVAILABLE,
          supplierBatchNumber: "BATCH-999",
          supplierId: "sup-123",
          type: "Updated Material",
        })
      );
      expect(result.type).toBe("Updated Material");
      expect(result.status).toBe(MaterialStatus.AVAILABLE);
    });

    describe("Role-based validation", () => {
      it("should allow FEEDSTOCK_RECYCLING_MEMBERS to update RECYCLE_POLYMERS materials", async () => {
        const recycleExistingMaterial = {
          ...existingMaterial,
          family: MaterialFamily.RECYCLE_POLYMERS,
        };
        const updateDto = {
          family: MaterialFamily.RECYCLE_POLYMERS,
          supplierBatchNumber: "BATCH-123",
          type: "Updated Recycled PET",
        };

        mockMaterialRepository.findById.mockResolvedValue(recycleExistingMaterial);
        mockMaterialRepository.update.mockResolvedValue({ ...recycleExistingMaterial, ...updateDto });

        const result = await service.updateMaterial(materialId, updateDto, USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS);

        expect(mockMaterialRepository.update).toHaveBeenCalledWith(materialId, {
          family: MaterialFamily.RECYCLE_POLYMERS,
          supplierBatchNumber: "BATCH-123",
          type: "Updated Recycled PET",
        });
        expect(result.type).toBe("Updated Recycled PET");
      });

      it("should throw ForbiddenException when FEEDSTOCK_RECYCLING_MEMBERS tries to update non-RECYCLE_POLYMERS material", async () => {
        const updateDto = {
          family: MaterialFamily.POLYMERS,
          supplierBatchNumber: "BATCH-123",
          type: "Updated",
        };

        mockMaterialRepository.findById.mockResolvedValue(existingMaterial);

        await expect(
          service.updateMaterial(materialId, updateDto, USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS)
        ).rejects.toThrow(ForbiddenException);
        expect(mockMaterialRepository.update).not.toHaveBeenCalled();
      });

      it("should throw ForbiddenException when non-FEEDSTOCK roles try to update RECYCLE_POLYMERS material", async () => {
        const recycleExistingMaterial = {
          ...existingMaterial,
          family: MaterialFamily.RECYCLE_POLYMERS,
        };

        const updateDto = {
          family: MaterialFamily.RECYCLE_POLYMERS,
          supplierBatchNumber: "BATCH-123",
          type: "Updated",
        };

        mockMaterialRepository.findById.mockResolvedValue(recycleExistingMaterial);

        await expect(
          service.updateMaterial(materialId, updateDto, USER_ROLE.ADMIN)
        ).rejects.toThrow(ForbiddenException);

        await expect(
          service.updateMaterial(materialId, updateDto, USER_ROLE.MATERIAL_MANAGER)
        ).rejects.toThrow(ForbiddenException);

        expect(mockMaterialRepository.update).not.toHaveBeenCalled();
      });

      it("should throw ForbiddenException when trying to change family to RECYCLE_POLYMERS without proper role", async () => {
        const updateDto = {
          family: MaterialFamily.RECYCLE_POLYMERS,
          supplierBatchNumber: "BATCH-123",
        };

        mockMaterialRepository.findById.mockResolvedValue(existingMaterial);

        await expect(
          service.updateMaterial(materialId, updateDto, USER_ROLE.ADMIN)
        ).rejects.toThrow(ForbiddenException);

        await expect(
          service.updateMaterial(materialId, updateDto, USER_ROLE.MATERIAL_MANAGER)
        ).rejects.toThrow(ForbiddenException);

        expect(mockMaterialRepository.update).not.toHaveBeenCalled();
      });

      it("should allow ADMIN and MATERIAL_MANAGER to update non-RECYCLE_POLYMERS materials", async () => {
        const updateDto = {
          family: MaterialFamily.POLYMERS,
          supplierBatchNumber: "BATCH-123",
          type: "Updated by Admin",
        };
        const updatedMaterial = { ...existingMaterial, ...updateDto };

        mockMaterialRepository.findById.mockResolvedValue(existingMaterial);
        mockMaterialRepository.update.mockResolvedValue(updatedMaterial);

        const adminResult = await service.updateMaterial(materialId, updateDto, USER_ROLE.ADMIN);
        expect(adminResult.type).toBe("Updated by Admin");

        const managerResult = await service.updateMaterial(materialId, updateDto, USER_ROLE.MATERIAL_MANAGER);
        expect(managerResult.type).toBe("Updated by Admin");
      });
    });

    describe("material status change events", () => {
      it("should emit event when material status changes from AVAILABLE to UNDER_REVIEW", async () => {
        const materialId1 = "material-1";
        const existingMaterial1 = {
          family: MaterialFamily.POLYMERS,
          id: materialId1,
          reference: "MAT-001",
          status: MaterialStatus.AVAILABLE,
          supplier: { name: "Test Supplier" },
          type: "Polymer",
        };
        const updateDto1 = {
          family: MaterialFamily.POLYMERS,
          status: MaterialStatus.UNDER_REVIEW,
          supplierBatchNumber: "BATCH-123",
        };
        const updatedMaterial1 = { ...existingMaterial1, ...updateDto1 };

        const mockFeedstockUsers = {
          data: [
            { id: "feedstock-user-1" },
            { id: "feedstock-user-2" },
          ],
        };

        mockMaterialRepository.findById.mockResolvedValue(existingMaterial1);
        mockMaterialRepository.update.mockResolvedValue(updatedMaterial1);
        mockUserService.findAll.mockResolvedValue(mockFeedstockUsers);

        await service.updateMaterial(materialId1, updateDto1, USER_ROLE.ADMIN);

        expect(mockEventEmitter.emit).toHaveBeenCalledWith(
          "material.status-changed",
          new MaterialStatusChangedEvent(
            MaterialStatus.UNDER_REVIEW,
            ["feedstock-user-1", "feedstock-user-2"],
            "Polymer"
          )
        );
        expect(mockUserService.findAll).toHaveBeenCalledWith({ roleCode: USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS });
      });

      it("should emit event when material status changes from UNDER_REVIEW to AVAILABLE", async () => {
        const materialId2 = "material-2";
        const existingMaterial2 = {
          family: MaterialFamily.POLYMERS,
          id: materialId2,
          reference: "MAT-002",
          status: MaterialStatus.UNDER_REVIEW,
          supplier: { name: "Test Supplier" },
          type: "Polymer",
        };
        const updateDto2 = {
          family: MaterialFamily.POLYMERS,
          status: MaterialStatus.AVAILABLE,
          supplierBatchNumber: "BATCH-456",
        };
        const updatedMaterial2 = { ...existingMaterial2, ...updateDto2 };

        const mockAllUsers = {
          data: [
            { id: "user-1" },
            { id: "user-2" },
            { id: "user-3" },
          ],
        };

        mockMaterialRepository.findById.mockResolvedValue(existingMaterial2);
        mockMaterialRepository.update.mockResolvedValue(updatedMaterial2);
        mockUserService.findAll.mockResolvedValue(mockAllUsers);

        await service.updateMaterial(materialId2, updateDto2, USER_ROLE.ADMIN);

        expect(mockEventEmitter.emit).toHaveBeenCalledWith(
          "material.status-changed",
          new MaterialStatusChangedEvent(
            MaterialStatus.AVAILABLE,
            ["user-1", "user-2", "user-3"],
            "Polymer"
          )
        );
        expect(mockUserService.findAll).toHaveBeenCalledWith({});
      });

      it("should emit event when material status changes to ARCHIVE", async () => {
        const materialId3 = "material-3";
        const existingMaterial3 = {
          family: MaterialFamily.POLYMERS,
          id: materialId3,
          reference: "MAT-003",
          status: MaterialStatus.AVAILABLE,
          supplier: { name: "Test Supplier" },
          type: "Polymer",
        };
        const updateDto3 = {
          family: MaterialFamily.POLYMERS,
          status: MaterialStatus.ARCHIVE,
          supplierBatchNumber: "BATCH-789",
        };
        const updatedMaterial3 = { ...existingMaterial3, ...updateDto3 };

        const mockAllUsers = {
          data: [
            { id: "user-1" },
            { id: "user-2" },
          ],
        };

        mockMaterialRepository.findById.mockResolvedValue(existingMaterial3);
        mockMaterialRepository.update.mockResolvedValue(updatedMaterial3);
        mockUserService.findAll.mockResolvedValue(mockAllUsers);

        await service.updateMaterial(materialId3, updateDto3, USER_ROLE.ADMIN);

        expect(mockEventEmitter.emit).toHaveBeenCalledWith(
          "material.status-changed",
          new MaterialStatusChangedEvent(
            MaterialStatus.ARCHIVE,
            ["user-1", "user-2"],
            "Polymer"
          )
        );
        expect(mockUserService.findAll).toHaveBeenCalledWith({});
      });

      it("should not emit event when material status does not change", async () => {
        const materialId4 = "material-4";
        const existingMaterial4 = {
          family: MaterialFamily.POLYMERS,
          id: materialId4,
          reference: "MAT-004",
          status: MaterialStatus.AVAILABLE,
          supplier: { name: "Test Supplier" },
          type: "Polymer",
        };
        const updateDto4 = {
          family: MaterialFamily.POLYMERS,
          reference: "MAT-004-UPDATED",
          supplierBatchNumber: "BATCH-999",
        };
        const updatedMaterial4 = { ...existingMaterial4, ...updateDto4 };

        mockMaterialRepository.findById.mockResolvedValue(existingMaterial4);
        mockMaterialRepository.update.mockResolvedValue(updatedMaterial4);

        await service.updateMaterial(materialId4, updateDto4, USER_ROLE.ADMIN);

        expect(mockEventEmitter.emit).not.toHaveBeenCalled();
        expect(mockUserService.findAll).not.toHaveBeenCalled();
      });

      it("should handle empty user list for UNDER_REVIEW status", async () => {
        const materialId5 = "material-5";
        const existingMaterial5 = {
          family: MaterialFamily.POLYMERS,
          id: materialId5,
          reference: "MAT-005",
          status: MaterialStatus.AVAILABLE,
          supplier: { name: "Test Supplier" },
          type: "Polymer",
        };
        const updateDto5 = {
          family: MaterialFamily.POLYMERS,
          status: MaterialStatus.UNDER_REVIEW,
          supplierBatchNumber: "BATCH-000",
        };
        const updatedMaterial5 = { ...existingMaterial5, ...updateDto5 };

        const mockEmptyUsers = {
          data: [],
        };

        mockMaterialRepository.findById.mockResolvedValue(existingMaterial5);
        mockMaterialRepository.update.mockResolvedValue(updatedMaterial5);
        mockUserService.findAll.mockResolvedValue(mockEmptyUsers);

        await service.updateMaterial(materialId5, updateDto5, USER_ROLE.ADMIN);

        expect(mockEventEmitter.emit).toHaveBeenCalledWith(
          "material.status-changed",
          new MaterialStatusChangedEvent(
            MaterialStatus.UNDER_REVIEW,
            [],
            "Polymer"
          )
        );
        expect(mockUserService.findAll).toHaveBeenCalledWith({ roleCode: USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS });
      });
    });

    describe("mapToMaterialPageResponse familyData mapping", () => {
      const baseMaterial = {
        family: undefined,
        id: "mat-1",
        origin: "Asia",
        reference: "REF-1",
        status: MaterialStatus.AVAILABLE,
        supplier: { id: "sup-1", name: "Supplier1" },
        supplierBatchNumber: "BATCH-1",
        supplierId: "sup-1",
        type: "Polymer",
      };

      it("should map polymer family", () => {
        const material = {
          ...baseMaterial,
          family: MaterialFamily.POLYMERS,
          polymer: { id: "p1", mfiAv: 1.1 },
        } as unknown as MaterialWithFamilyData;
        const result = service["mapToMaterialPageResponse"](material);
        expect(result.familyData).toMatchObject({ id: "p1", mfiAv: 1.1 });
      });

      it("should map filler family", () => {
        const material = {
          ...baseMaterial,
          family: MaterialFamily.FILLERS,
          filler: { densityAv: 2.2, id: "f1" },
        } as unknown as MaterialWithFamilyData;
        const result = service["mapToMaterialPageResponse"](material);
        expect(result.familyData).toMatchObject({ densityAv: 2.2, id: "f1" });
      });

      it("should map elastomer family", () => {
        const material = {
          ...baseMaterial,
          elastomer: { id: "e1", tensileModulusAv: 3.3 },
          family: MaterialFamily.ELASTOMERS,
        } as unknown as MaterialWithFamilyData;
        const result = service["mapToMaterialPageResponse"](material);
        expect(result.familyData).toMatchObject({ id: "e1", tensileModulusAv: 3.3 });
      });

      it("should map additive family", () => {
        const material = {
          additive: { id: "a1", notchedIzodAv: 4.4 },
          ...baseMaterial,
          family: MaterialFamily.ADDITIVES,
        } as unknown as MaterialWithFamilyData;
        const result = service["mapToMaterialPageResponse"](material);
        expect(result.familyData).toMatchObject({ id: "a1", notchedIzodAv: 4.4 });
      });

      it("should map recycle polymer family", () => {
        const material = {
          ...baseMaterial,
          family: MaterialFamily.RECYCLE_POLYMERS,
          recyclePolymer: { cma23EnergyPunctureAv: 5.5, id: "r1" },
        } as unknown as MaterialWithFamilyData;
        const result = service["mapToMaterialPageResponse"](material);
        expect(result.familyData).toMatchObject({ cma23EnergyPunctureAv: 5.5, id: "r1" });
      });

      it("should map default family to undefined", () => {
        const material = {
          ...baseMaterial,
          family: "UNKNOWN" as MaterialFamily,
        } as unknown as MaterialWithFamilyData;
        const result = service["mapToMaterialPageResponse"](material);
        expect(result.familyData).toBeUndefined();
      });
    });

    describe("getColumnFilterValues", () => {
      it("should return paginated filter values from repository", async () => {
        const mockFilterResult = {
          data: ["A", "B", "C"],
          limit: 10,
          page: 1,
          total: 3,
        };
        const filterRequest = {
          column: MATERIAL_FILTER_COLUMN.ORIGIN,
          limit: 10,
          page: 1,
          q: "",
        };
        mockMaterialFilterRepository.findDistinctColumnValues.mockResolvedValue(mockFilterResult);

        const result = await service.getColumnFilterValues(filterRequest);
        expect(result.data).toEqual(["A", "B", "C"]);
        expect(result.meta.total).toBe(3);
        expect(result.meta.currentPage).toBe(1);
        expect(result.meta.perPage).toBe(10);
        expect(mockMaterialFilterRepository.findDistinctColumnValues).toHaveBeenCalledWith(
          MATERIAL_FILTER_COLUMN.ORIGIN,
          "",
          1,
          10
        );
      });
    });
  });

  describe("batchUpdateMaterial", () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should return responses for successful batch update", async () => {
      const batchUpdateRequest: BatchUpdateMaterialRequestDto[] = [
        {
          family: MaterialFamily.POLYMERS,
          id: "material-1",
          reference: "REF-001",
          status: MaterialStatus.AVAILABLE,
          supplierBatchNumber: "BATCH-001",
          type: "Polypropylene",
        },
        {
          family: MaterialFamily.ADDITIVES,
          id: "material-2",
          reference: "REF-002",
          status: MaterialStatus.AVAILABLE,
          supplierBatchNumber: "BATCH-002",
          type: "Antioxidant",
        },
      ];

      const mockMaterial1 = {
        family: MaterialFamily.POLYMERS,
        id: "material-1",
        origin: "Europe",
        reference: "REF-001",
        status: MaterialStatus.AVAILABLE,
        supplier: { name: "Test Supplier" },
        supplierBatchNumber: "BATCH-001",
        type: "Polypropylene",
      };

      const mockMaterial2 = {
        family: MaterialFamily.ADDITIVES,
        id: "material-2",
        origin: "Asia",
        reference: "REF-002",
        status: MaterialStatus.AVAILABLE,
        supplier: { name: "Test Supplier" },
        supplierBatchNumber: "BATCH-002",
        type: "Antioxidant",
      };

      const updatedMaterial1 = { ...mockMaterial1, ...batchUpdateRequest[0] };
      const updatedMaterial2 = { ...mockMaterial2, ...batchUpdateRequest[1] };

      mockMaterialRepository.findById
        .mockResolvedValueOnce(mockMaterial1)
        .mockResolvedValueOnce(mockMaterial2);
      mockMaterialRepository.update
        .mockResolvedValueOnce(updatedMaterial1)
        .mockResolvedValueOnce(updatedMaterial2);

      const result = await service.batchUpdateMaterial(batchUpdateRequest, USER_ROLE.MATERIAL_MANAGER);

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        id: "material-1",
        status: "fulfilled",
        value: {
          family: MaterialFamily.POLYMERS,
          id: "material-1",
          reference: "REF-001",
          status: MaterialStatus.AVAILABLE,
          supplier: "Test Supplier",
          supplierBatchNumber: "BATCH-001",
          type: "Polypropylene",
        },
      });
      expect(result[1]).toMatchObject({
        id: "material-2",
        status: "fulfilled",
        value: {
          family: MaterialFamily.ADDITIVES,
          id: "material-2",
          reference: "REF-002",
          status: MaterialStatus.AVAILABLE,
          supplier: "Test Supplier",
          supplierBatchNumber: "BATCH-002",
          type: "Antioxidant",
        },
      });
    });

    it("should handle batch update with mixed results", async () => {
      const batchUpdateRequest: BatchUpdateMaterialRequestDto[] = [
        {
          family: MaterialFamily.POLYMERS,
          id: "material-1",
          reference: "REF-001",
          status: MaterialStatus.AVAILABLE,
          supplierBatchNumber: "BATCH-001",
          type: "Polypropylene",
        },
        {
          family: MaterialFamily.ADDITIVES,
          id: "invalid-material",
          reference: "REF-002",
          status: MaterialStatus.AVAILABLE,
          supplierBatchNumber: "BATCH-002",
          type: "Antioxidant",
        },
      ];

      const mockMaterial1 = {
        family: MaterialFamily.POLYMERS,
        id: "material-1",
        origin: "Europe",
        reference: "REF-001",
        status: MaterialStatus.AVAILABLE,
        supplier: { name: "Test Supplier" },
        supplierBatchNumber: "BATCH-001",
        type: "Polypropylene",
      };

      const updatedMaterial1 = { ...mockMaterial1, ...batchUpdateRequest[0] };

      mockMaterialRepository.findById.mockImplementation((id: string) => {
        if (id === "material-1") {
          return Promise.resolve(mockMaterial1);
        }
        return Promise.resolve();
      });
      mockMaterialRepository.update.mockResolvedValueOnce(updatedMaterial1);

      const result = await service.batchUpdateMaterial(batchUpdateRequest, USER_ROLE.ADMIN);

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        id: "material-1",
        status: "fulfilled",
        value: {
          family: MaterialFamily.POLYMERS,
          id: "material-1",
          reference: "REF-001",
          status: MaterialStatus.AVAILABLE,
          supplier: "Test Supplier",
          supplierBatchNumber: "BATCH-001",
          type: "Polypropylene",
        },
      });
      expect(result[1]).toMatchObject({
        id: "invalid-material",
        reason: "Material with ID invalid-material not found",
        status: "rejected",
      });
    });

    it("should handle empty batch update request", async () => {
      const batchUpdateRequest: BatchUpdateMaterialRequestDto[] = [];

      const result = await service.batchUpdateMaterial(batchUpdateRequest, USER_ROLE.MATERIAL_MANAGER);

      expect(result).toEqual([]);
    });

    it("should handle different user roles", async () => {
      const batchUpdateRequest: BatchUpdateMaterialRequestDto[] = [
        {
          family: MaterialFamily.POLYMERS,
          id: "material-1",
          supplierBatchNumber: "BATCH-001",
        },
      ];

      const mockMaterial = {
        family: MaterialFamily.POLYMERS,
        id: "material-1",
        origin: "Europe",
        reference: "REF-001",
        status: MaterialStatus.AVAILABLE,
        supplier: { name: "Test Supplier" },
        supplierBatchNumber: "BATCH-001",
        type: "Polymer",
      };

      const updatedMaterial = { ...mockMaterial, supplierBatchNumber: "BATCH-001" };

      mockMaterialRepository.findById.mockResolvedValue(mockMaterial);
      mockMaterialRepository.update.mockResolvedValue(updatedMaterial);

      const adminResult = await service.batchUpdateMaterial(batchUpdateRequest, USER_ROLE.ADMIN);
      const materialManagerResult = await service.batchUpdateMaterial(batchUpdateRequest, USER_ROLE.MATERIAL_MANAGER);

      expect(adminResult).toHaveLength(1);
      expect(materialManagerResult).toHaveLength(1);
      expect(adminResult[0].status).toBe("fulfilled");
      expect(materialManagerResult[0].status).toBe("fulfilled");
    });

    it("should handle role permission errors", async () => {
      const batchUpdateRequest: BatchUpdateMaterialRequestDto[] = [
        {
          family: MaterialFamily.RECYCLE_POLYMERS,
          id: "material-1",
          supplierBatchNumber: "BATCH-001",
        },
      ];

      const mockMaterial = {
        family: MaterialFamily.RECYCLE_POLYMERS,
        id: "material-1",
        origin: "Europe",
        reference: "REF-001",
        status: MaterialStatus.AVAILABLE,
        supplier: { name: "Test Supplier" },
        supplierBatchNumber: "BATCH-001",
        type: "Recycled Polymer",
      };

      mockMaterialRepository.findById.mockResolvedValue(mockMaterial);

      const adminResult = await service.batchUpdateMaterial(batchUpdateRequest, USER_ROLE.ADMIN);

      expect(adminResult).toHaveLength(1);
      expect(adminResult[0].status).toBe("rejected");
      expect(adminResult[0].reason).toContain("feedstock role can update materials with family RECYCLE_POLYMERS");
    });

    it("should handle requests without user role", async () => {
      const batchUpdateRequest: BatchUpdateMaterialRequestDto[] = [
        {
          family: MaterialFamily.POLYMERS,
          id: "material-1",
          supplierBatchNumber: "BATCH-001",
        },
      ];

      const mockMaterial = {
        family: MaterialFamily.POLYMERS,
        id: "material-1",
        origin: "Europe",
        reference: "REF-001",
        status: MaterialStatus.AVAILABLE,
        supplier: { name: "Test Supplier" },
        supplierBatchNumber: "BATCH-001",
        type: "Polymer",
      };

      const updatedMaterial = { ...mockMaterial, supplierBatchNumber: "BATCH-001" };

      mockMaterialRepository.findById.mockResolvedValue(mockMaterial);
      mockMaterialRepository.update.mockResolvedValue(updatedMaterial);

      const result = await service.batchUpdateMaterial(batchUpdateRequest);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: "material-1",
        status: "fulfilled",
        value: {
          family: MaterialFamily.POLYMERS,
          id: "material-1",
          supplier: "Test Supplier",
        },
      });
    });

    it("should properly handle parallel processing", async () => {
      const batchUpdateRequest: BatchUpdateMaterialRequestDto[] = [
        {
          family: MaterialFamily.POLYMERS,
          id: "material-1",
          supplierBatchNumber: "BATCH-001",
        },
        {
          family: MaterialFamily.POLYMERS,
          id: "material-2",
          supplierBatchNumber: "BATCH-002",
        },
      ];

      const mockMaterial1 = {
        family: MaterialFamily.POLYMERS,
        id: "material-1",
        origin: "Europe",
        reference: "REF-001",
        status: MaterialStatus.AVAILABLE,
        supplier: { name: "Test Supplier" },
        supplierBatchNumber: "BATCH-001",
        type: "Polymer",
      };

      const mockMaterial2 = {
        family: MaterialFamily.POLYMERS,
        id: "material-2",
        origin: "Asia",
        reference: "REF-002",
        status: MaterialStatus.AVAILABLE,
        supplier: { name: "Test Supplier" },
        supplierBatchNumber: "BATCH-002",
        type: "Polymer",
      };

      mockMaterialRepository.findById
        .mockResolvedValueOnce(mockMaterial1)
        .mockResolvedValueOnce(mockMaterial2);
      mockMaterialRepository.update
        .mockResolvedValueOnce({ ...mockMaterial1, supplierBatchNumber: "BATCH-001" })
        .mockResolvedValueOnce({ ...mockMaterial2, supplierBatchNumber: "BATCH-002" });

      const result = await service.batchUpdateMaterial(batchUpdateRequest, USER_ROLE.ADMIN);

      expect(result).toHaveLength(2);
      expect(mockMaterialRepository.findById).toHaveBeenCalledTimes(2);
      expect(mockMaterialRepository.update).toHaveBeenCalledTimes(2);
    });
  });
});
