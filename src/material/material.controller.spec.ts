import { Test, TestingModule } from "@nestjs/testing";
import { AuthGuard } from "../auth/auth.guard";
import { RoleGuard } from "../auth/role.guard";
import { AuthenticatedRequest } from "../common";
import { USER_ROLE } from "../role/role.types";
import { BatchUpdateMaterialRequestDto, BatchUpdateMaterialResponseDto, MaterialSearchPageRequestDto, MaterialSearchRequestDto, UpdateMaterialDto } from "./dto";
import { MaterialComparisonService } from "./material-comparison.service";
import { MaterialExportService } from "./material-export.service";
import { MaterialController } from "./material.controller";
import { MaterialService } from "./material.service";
import { CriteriaOperator } from "@/common/dto/criteria-operator.dto";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

describe("MaterialController", () => {
  let controller: MaterialController;

  const mockMaterialService = {
    batchUpdateMaterial: jest.fn(),
    getMaterialOrigins: jest.fn(),
    searchMaterials: jest.fn(),
    searchMaterialsWithCriteria: jest.fn(),
    updateMaterial: jest.fn(),
  };

  const mockMaterialComparisonService = {
    compareMaterials: jest.fn(),
  };

  const mockMaterialExportService = {
    exportMaterials: jest.fn(),
  };

  const mockAuthGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  const mockRoleGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [MaterialController],
      providers: [
        {
          provide: MaterialService,
          useValue: mockMaterialService,
        },
        {
          provide: MaterialComparisonService,
          useValue: mockMaterialComparisonService,
        },
        {
          provide: MaterialExportService,
          useValue: mockMaterialExportService,
        },
      ],
    })
      .overrideGuard(AuthGuard)
      .useValue(mockAuthGuard)
      .overrideGuard(RoleGuard)
      .useValue(mockRoleGuard)
      .compile();

    controller = module.get<MaterialController>(MaterialController);
  });

  describe("getMaterials", () => {
    it("should get materials by origin", async () => {
      const expectedResult = {
        data: [
          {
            family: "POLYMERS",
            id: "material-1",
            origin: "Europe",
            reference: "MAT-001",
            status: "AVAILABLE",
            supplier: "Test Supplier",
            supplierBatchNumber: "BATCH-123",
            type: "Polymer",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockMaterialService.searchMaterials.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchRequestDto = { origin: "Europe" };
      const result = await controller.getMaterials(searchRequest);

      expect(mockMaterialService.searchMaterials).toHaveBeenCalledWith({
        origin: "Europe",
        userRole: undefined,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should get materials by search parameter", async () => {
      const expectedResult = {
        data: [
          {
            family: "POLYMERS",
            id: "material-1",
            origin: "Europe",
            reference: "MAT-001",
            status: "AVAILABLE",
            supplier: "Test Supplier",
            supplierBatchNumber: "BATCH-123",
            type: "Polymer",
          },
          {
            family: "POLYMERS",
            id: "material-2",
            origin: "Asia",
            reference: "MAT-002",
            status: "AVAILABLE",
            supplier: "Another Supplier",
            supplierBatchNumber: "BATCH-456",
            type: "Polymer Blend",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 2,
          total: 2,
        },
      };

      mockMaterialService.searchMaterials.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchRequestDto = { search: "polymer" };
      const result = await controller.getMaterials(searchRequest);

      expect(mockMaterialService.searchMaterials).toHaveBeenCalledWith({
        search: "polymer",
        userRole: undefined,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should get materials without filters", async () => {
      const expectedResult = {
        data: [],
        meta: {
          currentPage: 1,
          from: 0,
          lastPage: 0,
          perPage: 10,
          to: 0,
          total: 0,
        },
      };

      mockMaterialService.searchMaterials.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchRequestDto = {};
      const result = await controller.getMaterials(searchRequest);

      expect(mockMaterialService.searchMaterials).toHaveBeenCalledWith({
        userRole: undefined,
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe("getMaterialOrigins", () => {
    it("should return distinct material origins", async () => {
      const expectedOrigins = ["Asia", "Europe", "North America"];

      mockMaterialService.getMaterialOrigins.mockResolvedValue(expectedOrigins);

      const result = await controller.getMaterialOrigins();

      expect(mockMaterialService.getMaterialOrigins).toHaveBeenCalledWith();
      expect(result).toEqual(expectedOrigins);
      expect(result).toHaveLength(3);
    });

    it("should return empty array when no origins exist", async () => {
      mockMaterialService.getMaterialOrigins.mockResolvedValue([]);

      const result = await controller.getMaterialOrigins();

      expect(mockMaterialService.getMaterialOrigins).toHaveBeenCalledWith();
      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it("should handle single origin", async () => {
      const expectedOrigins = ["Europe"];

      mockMaterialService.getMaterialOrigins.mockResolvedValue(expectedOrigins);

      const result = await controller.getMaterialOrigins();

      expect(mockMaterialService.getMaterialOrigins).toHaveBeenCalledWith();
      expect(result).toEqual(expectedOrigins);
      expect(result).toHaveLength(1);
    });
  });

  describe("getMaterialsPage", () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should search materials with criteria and return materials with family data", async () => {
      const expectedResult = {
        data: [
          {
            family: MaterialFamily.POLYMERS,
            familyData: {
              densityAv: 0.95,
              mfiAv: 10.5,
              tensileModulusAv: 2500,
            },
            id: "material-1",
            origin: "Europe",
            reference: "MAT-001",
            status: MaterialStatus.AVAILABLE,
            supplier: "Test Supplier",
            supplierBatchNumber: "BATCH-123",
            type: "Polymer",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        family: MaterialFamily.POLYMERS,
        search: "polymer",
        status: MaterialStatus.AVAILABLE,
      };

      const request = { user: { role: USER_ROLE.ADMIN } } as AuthenticatedRequest;
      const result = await controller.getMaterialsPage(searchRequest, request);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        search: "polymer",
        status: MaterialStatus.AVAILABLE,
        userRole: USER_ROLE.ADMIN,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle array filters for origin and status", async () => {
      const expectedResult = {
        data: [
          {
            family: MaterialFamily.POLYMERS,
            id: "material-1",
            origin: "Europe",
            reference: "MAT-001",
            status: MaterialStatus.AVAILABLE,
            supplier: "Test Supplier",
            type: "Polymer",
          },
          {
            family: MaterialFamily.POLYMERS,
            id: "material-2",
            origin: "Asia",
            reference: "MAT-002",
            status: MaterialStatus.ARCHIVE,
            supplier: "Another Supplier",
            type: "Polymer",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 2,
          total: 2,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        family: MaterialFamily.POLYMERS,
        origin: ["Europe", "Asia"],
        status: [MaterialStatus.AVAILABLE, MaterialStatus.ARCHIVE],
      };

      const request = { user: { role: USER_ROLE.MATERIAL_MANAGER } } as AuthenticatedRequest;
      const result = await controller.getMaterialsPage(searchRequest, request);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        origin: ["Europe", "Asia"],
        status: [MaterialStatus.AVAILABLE, MaterialStatus.ARCHIVE],
        userRole: USER_ROLE.MATERIAL_MANAGER,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle family criteria filtering", async () => {
      const expectedResult = {
        data: [
          {
            family: MaterialFamily.POLYMERS,
            familyData: {
              densityAv: 0.98,
              mfiAv: 15.2,
              tensileModulusAv: 3000,
            },
            id: "material-1",
            reference: "MAT-001",
            status: MaterialStatus.AVAILABLE,
            type: "High-Performance Polymer",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        family: MaterialFamily.POLYMERS,
        familyCriteria: [
          {
            operator: CriteriaOperator.GREATER_THAN_OR_EQUAL,
            propertyName: "mfiAv",
            value: 10,
          },
          {
            maxValue: 5000,
            minValue: 2000,
            operator: CriteriaOperator.BETWEEN,
            propertyName: "tensileModulusAv",
          },
        ],
      };

      const request = { user: { role: USER_ROLE.ADMIN } } as AuthenticatedRequest;
      const result = await controller.getMaterialsPage(searchRequest, request);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        familyCriteria: [
          {
            operator: CriteriaOperator.GREATER_THAN_OR_EQUAL,
            propertyName: "mfiAv",
            value: 10,
          },
          {
            maxValue: 5000,
            minValue: 2000,
            operator: CriteriaOperator.BETWEEN,
            propertyName: "tensileModulusAv",
          },
        ],
        userRole: USER_ROLE.ADMIN,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle FEEDSTOCK_RECYCLING_MEMBERS role with recycle polymers", async () => {
      const expectedResult = {
        data: [
          {
            family: MaterialFamily.RECYCLE_POLYMERS,
            familyData: {
              color: "Natural",
              mfiAv: 8.5,
              pirPcrElv: "PCR",
            },
            id: "recycle-material-1",
            reference: "REC-001",
            status: MaterialStatus.AVAILABLE,
            type: "Recycled Polymer",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        family: MaterialFamily.RECYCLE_POLYMERS,
        search: "recycled",
      };

      const request = { user: { role: USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS } } as AuthenticatedRequest;
      const result = await controller.getMaterialsPage(searchRequest, request);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        family: MaterialFamily.RECYCLE_POLYMERS,
        search: "recycled",
        userRole: USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle pagination parameters", async () => {
      const expectedResult = {
        data: [],
        meta: {
          currentPage: 3,
          from: 11,
          lastPage: 10,
          perPage: 5,
          to: 15,
          total: 50,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        family: MaterialFamily.POLYMERS,
        limit: 5,
        page: 3,
      };

      const request = { user: { role: USER_ROLE.ADMIN } } as AuthenticatedRequest;
      const result = await controller.getMaterialsPage(searchRequest, request);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        limit: 5,
        page: 3,
        userRole: USER_ROLE.ADMIN,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle empty request with default pagination", async () => {
      const expectedResult = {
        data: [],
        meta: {
          currentPage: 1,
          from: 0,
          lastPage: 0,
          perPage: 10,
          to: 0,
          total: 0,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        family: MaterialFamily.POLYMERS,
      };
      const request = { user: { role: USER_ROLE.ADMIN } } as AuthenticatedRequest;
      const result = await controller.getMaterialsPage(searchRequest, request);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        userRole: USER_ROLE.ADMIN,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle request without user context", async () => {
      const expectedResult = {
        data: [],
        meta: {
          currentPage: 1,
          from: 0,
          lastPage: 0,
          perPage: 10,
          to: 0,
          total: 0,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        family: MaterialFamily.POLYMERS,
        search: "test",
      };

      const result = await controller.getMaterialsPage(searchRequest);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        search: "test",
        userRole: undefined,
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe("updateMaterial", () => {
    it("should update material successfully", async () => {
      const materialId = "material-1";
      const updateDto: UpdateMaterialDto = {
        family: MaterialFamily.POLYMERS,
        origin: "Updated Origin",
        status: MaterialStatus.AVAILABLE,
        supplierBatchNumber: "BATCH-123",
        type: "Updated Polypropylene",
      };

      const expectedResult = {
        family: "POLYMERS",
        id: materialId,
        origin: "Updated Origin",
        reference: "MAT-001",
        status: MaterialStatus.AVAILABLE,
        supplier: "Test Supplier",
        supplierBatchNumber: "BATCH-123",
        type: "Updated Polypropylene",
      };

      mockMaterialService.updateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: USER_ROLE.ADMIN } } as AuthenticatedRequest;
      const result = await controller.updateMaterial(materialId, updateDto, mockRequest);

      expect(mockMaterialService.updateMaterial).toHaveBeenCalledWith(materialId, updateDto, USER_ROLE.ADMIN);
      expect(result).toEqual(expectedResult);
    });

    it("should update material with partial data", async () => {
      const materialId = "material-2";
      const updateDto: UpdateMaterialDto = {
        family: MaterialFamily.FILLERS,
        status: MaterialStatus.ARCHIVE,
        supplierBatchNumber: "BATCH-456",
      };

      const expectedResult = {
        family: "FILLERS",
        id: materialId,
        origin: "Asia",
        reference: "MAT-002",
        status: MaterialStatus.ARCHIVE,
        supplier: "Filler Supplier",
        supplierBatchNumber: "BATCH-456",
        type: "Filler Material",
      };

      mockMaterialService.updateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: USER_ROLE.MATERIAL_MANAGER } } as AuthenticatedRequest;
      const result = await controller.updateMaterial(materialId, updateDto, mockRequest);

      expect(mockMaterialService.updateMaterial).toHaveBeenCalledWith(materialId, updateDto, USER_ROLE.MATERIAL_MANAGER);
      expect(result).toEqual(expectedResult);
    });

    it("should handle empty update DTO", async () => {
      const materialId = "material-3";
      const updateDto: UpdateMaterialDto = {
        family: MaterialFamily.ELASTOMERS,
        supplierBatchNumber: "BATCH-789",
      };

      const expectedResult = {
        family: "ELASTOMERS",
        id: materialId,
        origin: "South America",
        reference: "MAT-003",
        status: MaterialStatus.AVAILABLE,
        supplier: "Elastomer Supplier",
        supplierBatchNumber: "BATCH-789",
        type: "Elastomer Material",
      };

      mockMaterialService.updateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: USER_ROLE.ADMIN } } as AuthenticatedRequest;
      const result = await controller.updateMaterial(materialId, updateDto, mockRequest);

      expect(mockMaterialService.updateMaterial).toHaveBeenCalledWith(materialId, updateDto, USER_ROLE.ADMIN);
      expect(result).toEqual(expectedResult);
    });

    it("should update all material fields", async () => {
      const materialId = "material-4";
      const updateDto: UpdateMaterialDto = {
        family: MaterialFamily.ADDITIVES,
        origin: "North America",
        reference: "NEW-REF-001",
        status: MaterialStatus.UNDER_REVIEW,
        supplierBatchNumber: "NEW-BATCH-789",
        supplierId: "new-supplier-id",
        type: "New Material Type",
      };

      const expectedResult = {
        family: "ADDITIVES",
        id: materialId,
        origin: "North America",
        reference: "NEW-REF-001",
        status: MaterialStatus.UNDER_REVIEW,
        supplier: "New Supplier",
        supplierBatchNumber: "NEW-BATCH-789",
        type: "New Material Type",
      };

      mockMaterialService.updateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: USER_ROLE.ADMIN } } as AuthenticatedRequest;
      const result = await controller.updateMaterial(materialId, updateDto, mockRequest);

      expect(mockMaterialService.updateMaterial).toHaveBeenCalledWith(materialId, updateDto, USER_ROLE.ADMIN);
      expect(result).toEqual(expectedResult);
    });

    it("should handle material with null supplier", async () => {
      const materialId = "material-5";
      const updateDto: UpdateMaterialDto = {
        family: MaterialFamily.ADDITIVES,
        origin: "Unknown",
        supplierBatchNumber: "BATCH-000",
        type: "Updated Material with No Supplier",
      };

      const expectedResult = {
        family: "ADDITIVES",
        id: materialId,
        origin: "Unknown",
        reference: "MAT-005",
        status: MaterialStatus.AVAILABLE,
        supplier: undefined,
        supplierBatchNumber: "BATCH-000",
        type: "Updated Material with No Supplier",
      };

      mockMaterialService.updateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: USER_ROLE.MATERIAL_MANAGER } } as AuthenticatedRequest;
      const result = await controller.updateMaterial(materialId, updateDto, mockRequest);

      expect(mockMaterialService.updateMaterial).toHaveBeenCalledWith(materialId, updateDto, USER_ROLE.MATERIAL_MANAGER);
      expect(result).toEqual(expectedResult);
    });
  });
  describe("exportMaterials", () => {
    it("should call exportMaterials on the service and send .xlsx buffer with correct headers", async () => {
      const dto = {
        family: MaterialFamily.POLYMERS,
        search: "test",
        selectedFields: ["id", "mfiAv", "reference"],
      };
      const fakeBuffer = Buffer.from("fake-xlsx-content");
      mockMaterialExportService.exportMaterials.mockResolvedValue(fakeBuffer);

      const headerSpy = jest.fn();
      const sendSpy = jest.fn();
      const response: Partial<import("express").Response> = {
        header: headerSpy,
        send: sendSpy,
      };

      await controller.exportMaterials(dto, response as import("express").Response);

      expect(mockMaterialExportService.exportMaterials).toHaveBeenCalledWith(dto);
      expect(headerSpy).toHaveBeenCalledWith("Content-Disposition", expect.stringContaining("attachment; filename=\"materials-POLYMERS-"));
      expect(headerSpy).toHaveBeenCalledWith("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
      expect(headerSpy).toHaveBeenCalledWith("Content-Length", fakeBuffer.length.toString());
      expect(sendSpy).toHaveBeenCalledWith(fakeBuffer);
    });
  });

  describe("batchUpdateMaterial", () => {
    it("should batch update materials successfully", async () => {
      const batchUpdateRequest: BatchUpdateMaterialRequestDto[] = [
        {
          family: MaterialFamily.POLYMERS,
          familyData: { mfiAv: 27.8 },
          id: "material-1",
          origin: "Europe",
          reference: "REF-001",
          status: MaterialStatus.AVAILABLE,
          supplier: "IG 8-01",
          supplierBatchNumber: "BATCH-001",
          type: "Polypropylene",
        },
        {
          family: MaterialFamily.ADDITIVES,
          familyData: { anonymizationCode: "ANT_002" },
          id: "material-2",
          origin: "Asia",
          reference: "REF-002",
          status: MaterialStatus.AVAILABLE,
          supplier: "IG 8-01",
          supplierBatchNumber: "BATCH-002",
          type: "Antioxidant",
        },
      ];

      const expectedResult: BatchUpdateMaterialResponseDto[] = [
        {
          id: "material-1",
          status: "fulfilled",
          value: {
            family: MaterialFamily.POLYMERS,
            id: "material-1",
            origin: "Europe",
            reference: "REF-001",
            status: MaterialStatus.AVAILABLE,
            supplier: "IG 8-01",
            supplierBatchNumber: "BATCH-001",
            type: "Polypropylene",
          },
        },
        {
          id: "material-2",
          status: "fulfilled",
          value: {
            family: MaterialFamily.ADDITIVES,
            id: "material-2",
            origin: "Asia",
            reference: "REF-002",
            status: MaterialStatus.AVAILABLE,
            supplier: "IG 8-01",
            supplierBatchNumber: "BATCH-002",
            type: "Antioxidant",
          },
        },
      ];

      mockMaterialService.batchUpdateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: USER_ROLE.MATERIAL_MANAGER } } as AuthenticatedRequest;
      const result = await controller.batchUpdateMaterial(batchUpdateRequest, mockRequest);

      expect(mockMaterialService.batchUpdateMaterial).toHaveBeenCalledWith(batchUpdateRequest, USER_ROLE.MATERIAL_MANAGER);
      expect(result).toEqual(expectedResult);
    });

    it("should handle batch update with mixed results", async () => {
      const batchUpdateRequest: BatchUpdateMaterialRequestDto[] = [
        {
          family: MaterialFamily.POLYMERS,
          id: "material-1",
          reference: "REF-001",
          status: MaterialStatus.AVAILABLE,
          supplierBatchNumber: "BATCH-001",
          type: "Polypropylene",
        },
        {
          family: MaterialFamily.ADDITIVES,
          id: "invalid-material",
          reference: "REF-002",
          status: MaterialStatus.AVAILABLE,
          supplierBatchNumber: "BATCH-002",
          type: "Antioxidant",
        },
      ];

      const expectedResult: BatchUpdateMaterialResponseDto[] = [
        {
          id: "material-1",
          status: "fulfilled",
          value: {
            family: MaterialFamily.POLYMERS,
            id: "material-1",
            origin: "Asia",
            reference: "REF-001",
            status: MaterialStatus.AVAILABLE,
            supplier: "Supplier123",
            supplierBatchNumber: "BATCH-001",
            type: "Polypropylene",
          },
        },
        {
          id: "invalid-material",
          reason: "Material not found",
          status: "rejected",
        },
      ];

      mockMaterialService.batchUpdateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: USER_ROLE.ADMIN } } as AuthenticatedRequest;
      const result = await controller.batchUpdateMaterial(batchUpdateRequest, mockRequest);

      expect(mockMaterialService.batchUpdateMaterial).toHaveBeenCalledWith(batchUpdateRequest, USER_ROLE.ADMIN);
      expect(result).toEqual(expectedResult);
    });

    it("should handle empty batch update request", async () => {
      const batchUpdateRequest: BatchUpdateMaterialRequestDto[] = [];
      const expectedResult: BatchUpdateMaterialResponseDto[] = [];

      mockMaterialService.batchUpdateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: USER_ROLE.MATERIAL_MANAGER } } as AuthenticatedRequest;
      const result = await controller.batchUpdateMaterial(batchUpdateRequest, mockRequest);

      expect(mockMaterialService.batchUpdateMaterial).toHaveBeenCalledWith(batchUpdateRequest, USER_ROLE.MATERIAL_MANAGER);
      expect(result).toEqual(expectedResult);
    });

    it("should pass userRole correctly for different user types", async () => {
      const batchUpdateRequest: BatchUpdateMaterialRequestDto[] = [
        {
          family: MaterialFamily.POLYMERS,
          id: "material-1",
          reference: "REF-001",
          supplierBatchNumber: "BATCH-001",
        },
      ];

      const expectedResult: BatchUpdateMaterialResponseDto[] = [
        {
          id: "material-1",
          status: "fulfilled",
          value: {
            family: MaterialFamily.POLYMERS,
            id: "material-1",
            origin: "Asia",
            reference: "REF-001",
            status: MaterialStatus.AVAILABLE,
            supplier: "Supplier123",
            supplierBatchNumber: "BATCH-001",
            type: "Mock Material Type",
          },
        },
      ];

      mockMaterialService.batchUpdateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS } } as AuthenticatedRequest;
      await controller.batchUpdateMaterial(batchUpdateRequest, mockRequest);

      expect(mockMaterialService.batchUpdateMaterial).toHaveBeenCalledWith(batchUpdateRequest, USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS);
    });

    it("should handle request without user context", async () => {
      const batchUpdateRequest: BatchUpdateMaterialRequestDto[] = [
        {
          family: MaterialFamily.POLYMERS,
          id: "material-1",
          reference: "REF-001",
          supplierBatchNumber: "BATCH-001",
        },
      ];

      const expectedResult: BatchUpdateMaterialResponseDto[] = [
        {
          id: "material-1",
          status: "fulfilled",
          value: {
            family: MaterialFamily.POLYMERS,
            id: "material-1",
            origin: "Asia",
            reference: "REF-001",
            status: MaterialStatus.AVAILABLE,
            supplier: "Supplier123",
            supplierBatchNumber: "BATCH-001",
            type: "Mock Material Type",
          },
        },
      ];

      mockMaterialService.batchUpdateMaterial.mockResolvedValue(expectedResult);

      const result = await controller.batchUpdateMaterial(batchUpdateRequest);

      expect(mockMaterialService.batchUpdateMaterial).toHaveBeenCalledWith(batchUpdateRequest, undefined);
      expect(result).toEqual(expectedResult);
    });
  });
});
