import { BadRequestException, NotFoundException, UnprocessableEntityException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { USER_ROLE } from "../role/role.types";
import { MaterialComparisonService } from "./material-comparison.service";
import { MaterialComparisonRepository } from "./repositories/material-comparison.repository";
import { PropertyMetadataService } from "./services/property-metadata.service";
import { MaterialFamily, MaterialStatus, PropertyType } from "@/generated/prisma";

describe("MaterialComparisonService", () => {
  let service: MaterialComparisonService;
  let materialComparisonRepository: MaterialComparisonRepository;
  let propertyMetadataService: PropertyMetadataService;

  const mockMaterial = {
    family: MaterialFamily.ADDITIVES,
    id: "id-1",
    origin: "origin-1",
    properties: { prop1: "value1" },
    reference: "ref-1",
    status: MaterialStatus.AVAILABLE,
    supplierBatchNumber: "batch-1",
    supplierId: "sup-id-1",
    supplierName: "sup-1",
    type: "type-1",
  };

  const mockMetadata = {
    prop1: { description: "desc", type: PropertyType.NUMBER, unit: "mm" },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MaterialComparisonService,
        {
          provide: MaterialComparisonRepository,
          useValue: {
            getMaterialsForComparison: jest.fn(),
          },
        },
        {
          provide: PropertyMetadataService,
          useValue: {
            getConsolidatedPropertyMetadata: jest.fn(),
            getPropertyMetadataByFamily: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<MaterialComparisonService>(MaterialComparisonService);
    materialComparisonRepository = module.get<MaterialComparisonRepository>(MaterialComparisonRepository);
    propertyMetadataService = module.get<PropertyMetadataService>(PropertyMetadataService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("compareMaterials", () => {
    it("should throw BadRequestException if no material IDs are provided", async () => {
      await expect(service.compareMaterials({ materialIds: [] })).rejects.toThrow(
        new BadRequestException("At least one material ID must be provided"),
      );
    });

    it("should throw UnprocessableEntityException if more than 20 material IDs are provided", async () => {
      const materialIds = Array.from({ length: 21 }, (_, index) => `id-${index}`);
      await expect(service.compareMaterials({ materialIds })).rejects.toThrow(
        new UnprocessableEntityException("Maximum 20 materials can be compared at once"),
      );
    });

    it("should throw NotFoundException if no materials are found", async () => {
      jest.spyOn(materialComparisonRepository, "getMaterialsForComparison").mockResolvedValue([]);
      await expect(service.compareMaterials({ materialIds: ["id-1"] })).rejects.toThrow(
        new NotFoundException("No materials found with the provided IDs"),
      );
    });

    it("should throw NotFoundException for missing materials (non-engineer)", async () => {
      jest.spyOn(materialComparisonRepository, "getMaterialsForComparison").mockResolvedValue([mockMaterial]);
      await expect(service.compareMaterials({ materialIds: ["id-1", "id-2"] })).rejects.toThrow(
        new NotFoundException("Materials not found: id-2"),
      );
    });

    it("should throw NotFoundException for missing or unavailable materials (engineer)", async () => {
      jest.spyOn(materialComparisonRepository, "getMaterialsForComparison").mockResolvedValue([mockMaterial]);
      await expect(service.compareMaterials({ materialIds: ["id-1", "id-2"] }, USER_ROLE.ENGINEERS)).rejects.toThrow(
        new NotFoundException("Materials not found or not available: id-2. Engineers can only compare available materials."),
      );
    });

    it("should return comparison data when materials are found", async () => {
      const materialIds = ["id-1"];
      jest.spyOn(materialComparisonRepository, "getMaterialsForComparison").mockResolvedValue([mockMaterial]);
      jest.spyOn(propertyMetadataService, "getPropertyMetadataByFamily").mockResolvedValue(mockMetadata);
      jest.spyOn(propertyMetadataService, "getConsolidatedPropertyMetadata").mockResolvedValue(mockMetadata);

      const result = await service.compareMaterials({ materialIds });

      expect(result).toBeDefined();
      expect(result.materials).toEqual([mockMaterial]);
      expect(result.availableProperties).toEqual(["prop1"]);
      expect(result.propertyMetadata).toEqual(mockMetadata);
    });

    it("should return filtered comparison data when includeProperties is provided", async () => {
      const materialIds = ["id-1"];
      const materialWithMoreProperties = {
        ...mockMaterial,
        properties: { prop1: "value1", prop2: "value2" },
      };
      jest.spyOn(materialComparisonRepository, "getMaterialsForComparison").mockResolvedValue([materialWithMoreProperties]);
      jest.spyOn(propertyMetadataService, "getConsolidatedPropertyMetadata").mockResolvedValue(mockMetadata);

      const result = await service.compareMaterials({ includeProperties: ["prop1"], materialIds });

      expect(result.materials[0].properties).toEqual({ prop1: "value1" });
      expect(result.availableProperties).toEqual(["prop1"]);
    });

    it("should call getMaterialsForComparison with AVAILABLE status for engineers", async () => {
      const materialIds = ["id-1"];
      const getMaterialsSpy = jest.spyOn(materialComparisonRepository, "getMaterialsForComparison").mockResolvedValue([]);

      await expect(service.compareMaterials({ materialIds }, USER_ROLE.ENGINEERS)).rejects.toThrow(NotFoundException);
      expect(getMaterialsSpy).toHaveBeenCalledWith(materialIds, MaterialStatus.AVAILABLE);
    });

    it("should call getMaterialsForComparison without status for other roles", async () => {
      const materialIds = ["id-1"];
      const getMaterialsSpy = jest.spyOn(materialComparisonRepository, "getMaterialsForComparison").mockResolvedValue([]);

      await expect(service.compareMaterials({ materialIds }, USER_ROLE.ADMIN)).rejects.toThrow(NotFoundException);
      expect(getMaterialsSpy).toHaveBeenCalledWith(materialIds);
    });
  });
});
