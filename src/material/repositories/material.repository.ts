import { Injectable } from "@nestjs/common";
import { calculatePaginationOffset } from "../../common/utils";
import { FamilyCriteriaDto } from "../dto";
import { filterConditionBuilder, propertyValidator } from "../utils";
import type { Material, Supplier } from "@/generated/prisma";
import { MaterialFamily, MaterialStatus, Prisma } from "@/generated/prisma";
import { PrismaService } from "@/prisma.service";
import { USER_ROLE } from "@/role/role.types";

export interface MaterialWithSupplier extends Material {
  supplier?: Supplier | null
}

export interface MaterialFilters {
  family?: MaterialFamily
  limit?: number
  origin?: string | string[]
  page?: number
  reference?: string
  search?: string
  status?: MaterialStatus | MaterialStatus[]
  supplier?: string
  supplierBatchNumber?: string
  supplierId?: string
  userRole?: string
}

export interface PaginatedMaterialResult {
  data: Material[]
  limit: number
  page: number
  total: number
}

export interface UpdateMaterialData {
  family?: MaterialFamily
  familyData?: Record<string, unknown>
  origin?: string
  reference?: string
  status?: MaterialStatus
  supplierBatchNumber?: string
  supplierId?: string
  type?: string
}

@Injectable()
export class MaterialRepository {
  public constructor(private readonly prisma: PrismaService) {}

  public async findById(id: string): Promise<MaterialWithSupplier | null> {
    return await this.prisma.material.findUnique({
      include: {
        supplier: true,
      },
      where: { id },
    });
  }

  public async findByIds(ids: string[]): Promise<MaterialWithSupplier[]> {
    return await this.prisma.material.findMany({
      include: {
        additive: true,
        elastomer: true,
        filler: true,
        polymer: true,
        recyclePolymer: true,
        supplier: true,
      },
      where: { id: { in: ids } },
    });
  }

  public async update(id: string, materialData: UpdateMaterialData): Promise<MaterialWithSupplier> {
    const { familyData, ...basicData } = materialData;

    const updateData: Record<string, unknown> = { ...basicData };

    if (familyData && materialData.family) {
      const familyUpdateKey = this.getFamilyFilterKey(materialData.family);
      if (familyUpdateKey) {
        updateData[familyUpdateKey] = {
          update: familyData,
        };
      }
    }

    return await this.prisma.material.update({
      data: updateData as Prisma.MaterialUpdateInput,
      include: {
        additive: true,
        elastomer: true,
        filler: true,
        polymer: true,
        recyclePolymer: true,
        supplier: true,
      },
      where: { id },
    });
  }

  public async findDistinctOrigins(): Promise<{ origin: string }[]> {
    return await this.prisma.material.findMany({
      distinct: ["origin"],
      orderBy: {
        origin: "asc",
      },
      select: {
        origin: true,
      },
    });
  }

  public async findAll(filters: MaterialFilters): Promise<PaginatedMaterialResult> {
    const { limit = 10, page = 1 } = filters;
    const offset = calculatePaginationOffset(page, limit);

    const where = this.buildWhereClause(filters);

    const [data, total] = await Promise.all([
      this.prisma.material.findMany({
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: offset,
        take: limit,
        where,
      }),
      this.prisma.material.count({ where }),
    ]);

    return {
      data,
      limit,
      page,
      total,
    };
  }

  public async findWithFamilyFieldFilters(filters: MaterialFilters & { familyFilters?: FamilyCriteriaDto[] }): Promise<PaginatedMaterialResult> {
    const { familyFilters, limit = 10, page = 1 } = filters;
    const offset = calculatePaginationOffset(page, limit);

    const where = this.buildWhereClause(filters, true);
    this.applyFamilyFieldFilters(where, filters.family, familyFilters);

    const includeOptions: Prisma.MaterialInclude = {
      additive: true,
      elastomer: true,
      filler: true,
      polymer: true,
      recyclePolymer: true,
      supplier: true,
    };

    const [data, total] = await Promise.all([
      this.prisma.material.findMany({
        include: includeOptions,
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: offset,
        take: limit,
        where,
      }),
      this.prisma.material.count({ where }),
    ]);

    return {
      data,
      limit,
      page,
      total,
    };
  }

  private buildWhereClause(filters: MaterialFilters, applyUserRoleFilters = false): Prisma.MaterialWhereInput {
    const where: Prisma.MaterialWhereInput = {};

    this.applySearchFilter(where, filters.search);
    this.applyBasicFilters(where, filters);

    if (applyUserRoleFilters) {
      this.applyUserRoleFilter(where, filters.userRole, filters.family);
    }

    return where;
  }

  private applySearchFilter(where: Prisma.MaterialWhereInput, search?: string): void {
    if (search) {
      where.OR = [
        { origin: { contains: search, mode: "insensitive" } },
        { reference: { contains: search, mode: "insensitive" } },
        { supplier: { name: { contains: search, mode: "insensitive" } } },
        { supplierBatchNumber: { contains: search, mode: "insensitive" } },
        { type: { contains: search, mode: "insensitive" } },
      ];
    }
  }

  private applyBasicFilters(where: Prisma.MaterialWhereInput, filters: MaterialFilters): void {
    const { family, origin, reference, status, supplier, supplierBatchNumber, supplierId } = filters;

    if (origin) {
      where.origin = Array.isArray(origin)
        ? { in: origin }
        : { contains: origin, mode: "insensitive" };
    }
    if (family) {
      where.family = family;
    }
    if (status) {
      where.status = Array.isArray(status)
        ? { in: status }
        : status;
    }
    if (supplierId) {
      where.supplierId = supplierId;
    }
    if (supplier) {
      where.supplier = { name: { contains: supplier, mode: "insensitive" } };
    }
    if (supplierBatchNumber) {
      where.supplierBatchNumber = { contains: supplierBatchNumber, mode: "insensitive" };
    }
    if (reference) {
      where.reference = { contains: reference, mode: "insensitive" };
    }
  }

  private applyUserRoleFilter(where: Prisma.MaterialWhereInput, userRole?: string, family?: MaterialFamily): void {
    if (userRole === USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS) {
      where.family = MaterialFamily.RECYCLE_POLYMERS;
    }
    else if (!family) {
      where.family = { not: MaterialFamily.RECYCLE_POLYMERS };
    }
  }

  private applyFamilyFieldFilters(where: Prisma.MaterialWhereInput, family?: MaterialFamily, familyFilters?: FamilyCriteriaDto[]): void {
    if (!familyFilters || !family || !Array.isArray(familyFilters) || familyFilters.length === 0) {
      return;
    }

    propertyValidator.validateCriteria(family, familyFilters);

    const filterKey = this.getFamilyFilterKey(family);

    if (filterKey) {
      (where as Record<string, unknown>)[filterKey] = this.buildGenericFilters(familyFilters);
    }
  }

  private getFamilyFilterKey(family: MaterialFamily): string {
    const keyMap = {
      [MaterialFamily.POLYMERS]: "polymer",
      [MaterialFamily.FILLERS]: "filler",
      [MaterialFamily.ELASTOMERS]: "elastomer",
      [MaterialFamily.ADDITIVES]: "additive",
      [MaterialFamily.RECYCLE_POLYMERS]: "recyclePolymer",
    };

    return keyMap[family];
  }

  private buildGenericFilters(criteria: FamilyCriteriaDto[]): unknown {
    return filterConditionBuilder.buildGenericFilters(criteria);
  }
}
