import { Test, TestingModule } from "@nestjs/testing";
import { PropertyMetadataRepository } from "../repositories/property-metadata.repository";
import { PropertyMetadataService } from "./property-metadata.service";
import { MaterialFamily, PropertyType } from "@/generated/prisma";

describe("PropertyMetadataService", () => {
  let service: PropertyMetadataService;

  const mockFindByFamily = jest.fn();
  const mockFindByFamilies = jest.fn();

  const mockPropertyMetadata = [
    {
      description: "Density of the material",
      family: MaterialFamily.POLYMERS,
      id: "1",
      propertyKey: "density",
      type: PropertyType.NUMBER,
      unit: "g/cm³",
    },
    {
      description: "Melt Flow Index",
      family: MaterialFamily.POLYMERS,
      id: "2",
      propertyKey: "mfi",
      type: PropertyType.NUMBER,
      unit: "g/10min",
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PropertyMetadataService,
        {
          provide: PropertyMetadataRepository,
          useValue: {
            findByFamilies: mockFindByFamilies,
            findByFamily: mockFindByFamily,
          },
        },
      ],
    }).compile();

    service = module.get<PropertyMetadataService>(PropertyMetadataService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("getPropertyMetadataByFamily", () => {
    it("should return a map of property metadata for a family", async () => {
      mockFindByFamily.mockResolvedValue(mockPropertyMetadata);

      const result = await service.getPropertyMetadataByFamily(MaterialFamily.POLYMERS);

      expect(mockFindByFamily).toHaveBeenCalledWith(MaterialFamily.POLYMERS);
      expect(result).toEqual({
        density: {
          description: "Density of the material",
          type: PropertyType.NUMBER,
          unit: "g/cm³",
        },
        mfi: {
          description: "Melt Flow Index",
          type: PropertyType.NUMBER,
          unit: "g/10min",
        },
      });
    });

    it("should return an empty map if no properties are found for a family", async () => {
      mockFindByFamily.mockResolvedValue([]);
      const result = await service.getPropertyMetadataByFamily(MaterialFamily.POLYMERS);
      expect(result).toEqual({});
    });
  });

  describe("getConsolidatedPropertyMetadata", () => {
    it("should return a map of consolidated property metadata for multiple families", async () => {
      mockFindByFamilies.mockResolvedValue(mockPropertyMetadata);
      const families = [MaterialFamily.POLYMERS, MaterialFamily.FILLERS];
      const result = await service.getConsolidatedPropertyMetadata(families);

      expect(mockFindByFamilies).toHaveBeenCalledWith(families);
      expect(result).toEqual({
        density: {
          description: "Density of the material",
          type: PropertyType.NUMBER,
          unit: "g/cm³",
        },
        mfi: {
          description: "Melt Flow Index",
          type: PropertyType.NUMBER,
          unit: "g/10min",
        },
      });
    });

    it("should return an empty map if no properties are found for the families", async () => {
      mockFindByFamilies.mockResolvedValue([]);
      const result = await service.getConsolidatedPropertyMetadata([MaterialFamily.POLYMERS]);
      expect(result).toEqual({});
    });
  });
});
