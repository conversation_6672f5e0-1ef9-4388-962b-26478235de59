import { ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsEnum, IsNumber, IsOptional, IsString, Max, Min } from "class-validator";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

export class MaterialSearchRequestDto {
  @ApiPropertyOptional({
    description: "Search across all material fields (type, reference, family, supplier name, origin, supplier batch number)",
    example: "polymer",
  })
  @IsOptional()
  @IsString()
  public search?: string;

  @ApiPropertyOptional({
    description: "Filter materials by origin (e.g., Europe, Asia, North America)",
  })
  @IsOptional()
  @IsString()
  public origin?: string;

  @ApiPropertyOptional({
    description: "Filter materials by family",
    enum: MaterialFamily,
    example: MaterialFamily.POLYMERS,
  })
  @IsOptional()
  @IsEnum(MaterialFamily)
  public family?: MaterialFamily;

  @ApiPropertyOptional({
    description: "Filter materials by status",
    enum: MaterialStatus,
    example: MaterialStatus.AVAILABLE,
  })
  @IsOptional()
  @IsEnum(MaterialStatus)
  public status?: MaterialStatus;

  @ApiPropertyOptional({
    description: "Filter materials by supplier name (partial match)",
  })
  @IsOptional()
  @IsString()
  public supplier?: string;

  @ApiPropertyOptional({
    description: "Filter materials by supplier batch number",
  })
  @IsOptional()
  @IsString()
  public supplierBatchNumber?: string;

  @ApiPropertyOptional({
    description: "Filter materials by reference",
  })
  @IsOptional()
  @IsString()
  public reference?: string;

  @ApiPropertyOptional({
    description: "Page number for pagination",
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  public page?: number = 1;

  @ApiPropertyOptional({
    description: "Number of items per page",
    example: 10,
    maximum: 100,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  public limit?: number = 10;
}
