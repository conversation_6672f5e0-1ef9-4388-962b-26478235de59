import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsOptional, IsString } from "class-validator";
import { MaterialSearchPageRequestDto } from "./material-search-page-request.dto";

interface StatisticFields {
  average?: string[]
  coefficientOfVariation?: string[]
  max?: string[]
  median?: string[]
  min?: string[]
  standardDeviation?: string[]
}

export class MaterialExportRequestDto extends MaterialSearchPageRequestDto {
  @ApiPropertyOptional({
    description: "Fields to export, combining MaterialSearchPageRequestDto and family property definitions.",
    example: ["reference", "mfiAv", "densityAv"],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  public selectedFields?: string[];

  @ApiPropertyOptional({
    description: "Export with statistics (e.g., average, min, max) for numeric properties",
  })
  @IsOptional()
  @IsBoolean()
  public statistics?: boolean;

  @ApiPropertyOptional({
    description: "Specific fields for which to calculate statistics",
    example: {
      average: ["mfiAv", "densityAv", "tensileStrengthAv"],
      coefficientOfVariation: ["mfiAv", "densityAv"],
      max: ["mfiAv", "densityAv"],
      median: ["mfiAv", "densityAv"],
      min: ["mfiAv", "densityAv"],
      standardDeviation: ["mfiAv", "densityAv"],
    },
  })
  @IsOptional()
  public statisticFields?: StatisticFields;
}
