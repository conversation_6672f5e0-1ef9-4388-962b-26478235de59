import { Test, TestingModule } from "@nestjs/testing";

import { PrismaService } from "../../prisma.service";
import { CriteriaDto } from "../dto/criteria.dto";
import { MaterialCriteriaDto } from "../dto/material-criteria.dto";
import { ComparisonRepository } from "./comparison.repository";
import { CRITERIA_OPERATOR } from "@/common/dto/criteria-operator.dto";
import { USER_ROLE } from "@/role/role.types";

describe("ComparisonRepository", () => {
  let repository: ComparisonRepository;

  const mockPrisma = {
    $queryRawUnsafe: jest.fn(),
    formulation: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ComparisonRepository,
        { provide: PrismaService, useValue: mockPrisma },
      ],
    }).compile();
    repository = module.get<ComparisonRepository>(ComparisonRepository);

    mockPrisma.formulation.findMany.mockResolvedValue([]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(repository).toBeDefined();
  });

  describe("getFormulations", () => {
    const mockFormulationData = {
      grade: "Grade A",
      id: "formulation-1",
      ["material_matches"]: 2,
      materials: [
        {
          materialId: "material-1",
          percentage: 50,
          reference: "PP-H 1200",
          type: "Virgin PP Homopolymer",
        },
        {
          materialId: "material-2",
          percentage: 30,
          reference: "PE-HD 8000",
          type: "Virgin PE High Density",
        },
      ],
      name: "Test Formulation",
      ["optional_matches"]: 1,
      ["test_results"]: [
        {
          condition: "23°C, 50% RH",
          id: "test-1",
          maxRange: 30,
          minRange: 20,
          propertyName: "tensile_strength",
          standard: "ASTM D638",
          testName: "Tensile Strength",
          value: 25.5,
        },
      ],
      tier: 1,
    } as Record<string, unknown>;

    const mockCountResult = [{ count: BigInt(5) }];

    beforeEach(() => {
      mockPrisma.$queryRawUnsafe
        .mockResolvedValueOnce([mockFormulationData])
        .mockResolvedValueOnce(mockCountResult);

      mockPrisma.formulation.findUnique.mockResolvedValue({
        ownerId: "owner-id",
        requests: [],
      });

      mockPrisma.formulation.findMany.mockResolvedValue([
        {
          id: "formulation-1",
          ownerId: "owner-id",
          requests: [],
        },
      ]);
    });

    it("should return paginated formulations with default parameters", async () => {
      const result = await repository.getFormulations();

      expect(mockPrisma.$queryRawUnsafe).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        formulations: expect.arrayContaining([
          expect.objectContaining({
            formulationId: "formulation-1",
            grade: "Grade A",
            name: "Test Formulation",
            tier: 1,
          }),
        ]) as unknown[],
        total: 5,
      });
    });

    it("should apply pagination correctly", async () => {
      const page = 2;
      const limit = 5;

      await repository.getFormulations(page, limit);

      expect(mockPrisma.$queryRawUnsafe).toHaveBeenCalledWith(
        expect.any(String),
        expect.anything(),
        expect.anything(),
      );
    });

    it("should handle criteria filtering", async () => {
      const criteria: CriteriaDto[] = [
        {
          operator: CRITERIA_OPERATOR.GREATER_THAN_OR_EQUAL,
          propertyName: "tensile_strength",
          required: true,
          value: 20,
          valueTo: 100,
        },
        {
          operator: CRITERIA_OPERATOR.LESS_THAN_OR_EQUAL,
          propertyName: "mfi",
          required: false,
          tier: 1,
          value: 50,
          valueTo: 100,
        },
      ];

      await repository.getFormulations(1, 10, "owner-id", criteria);

      expect(mockPrisma.$queryRawUnsafe).toHaveBeenCalledTimes(2);
    });

    it("should handle material criteria filtering", async () => {
      const materialCriteria: MaterialCriteriaDto[] = [
        {
          materialId: "material-1",
          maxValue: 60,
          minValue: 40,
        },
      ];

      await repository.getFormulations(1, 10, "owner-id", undefined, materialCriteria);

      expect(mockPrisma.$queryRawUnsafe).toHaveBeenCalledTimes(2);
    });

    it("should handle combined criteria and material criteria", async () => {
      const criteria: CriteriaDto[] = [
        {
          operator: CRITERIA_OPERATOR.GREATER_THAN,
          propertyName: "impact_strength",
          required: true,
          value: 15,
          valueTo: 100,
        },
      ];

      const materialCriteria: MaterialCriteriaDto[] = [
        {
          materialId: "material-1",
          minValue: 25,
        },
      ];

      await repository.getFormulations(1, 10, "owner-id", criteria, materialCriteria, USER_ROLE.ADMIN);

      expect(mockPrisma.$queryRawUnsafe).toHaveBeenCalledTimes(2);
    });
  });
});
