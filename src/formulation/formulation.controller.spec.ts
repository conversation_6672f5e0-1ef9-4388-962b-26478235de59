import { ConfigService } from "@nestjs/config";
import { Reflector } from "@nestjs/core";
import { Test, TestingModule } from "@nestjs/testing";
import { AuthService } from "../auth/auth.service";
import { RoleGuard } from "../auth/role.guard";
import { AuthenticatedRequest } from "../common";
import { ComparisonService } from "./comparison.service";
import { ComparisonRequestDto, ComparisonResponseDto } from "./dto";
import { FormulationController } from "./formulation.controller";
import { FormulationService } from "./formulation.service";

describe("FormulationController", () => {
  let controller: FormulationController;

  const mockFormulationService = {
    createFormulations: jest.fn(),
    getFormulationById: jest.fn(),
    getFormulations: jest.fn(),
  };

  const mockComparisonService = {
    getComparisons: jest.fn(),
  };

  const mockAuthService = {
    validateToken: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockReflector = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FormulationController],
      providers: [
        {
          provide: FormulationService,
          useValue: mockFormulationService,
        },
        {
          provide: ComparisonService,
          useValue: mockComparisonService,
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: Reflector,
          useValue: mockReflector,
        },
        RoleGuard,
      ],
    }).compile();

    controller = module.get<FormulationController>(FormulationController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });

  describe("createFormulations", () => {
    it("should create formulations successfully", async () => {
      const dto = { materials: [], name: "Bumper", testResults: [] };
      const mockRequest = { user: { oid: "user-1" } } as AuthenticatedRequest;
      const expectedResult = { data: [{ grade: "A", id: "form-1", name: "Bumper", ownerId: "user-1" }] };
      mockFormulationService.createFormulations.mockResolvedValue(expectedResult);
      const result = await controller.createFormulations(dto as never, mockRequest);
      expect(mockFormulationService.createFormulations).toHaveBeenCalledWith(dto, "user-1");
      expect(result).toEqual(expectedResult);
    });
    it("should throw error if userId not found", async () => {
      const dto = { materials: [], name: "Bumper", testResults: [] };
      const mockRequest = { user: {} } as AuthenticatedRequest;
      await expect(controller.createFormulations(dto as never, mockRequest)).rejects.toThrow("User ID not found in request");
    });
    it("should get userId from headers if not in user", async () => {
      const dto = { materials: [], name: "Bumper", testResults: [] };
      const mockRequest = { headers: { oid: "header-user-1" } } as unknown as AuthenticatedRequest;
      const expectedResult = { data: [{ grade: "A", id: "form-2", name: "Bumper", ownerId: "header-user-1" }] };
      mockFormulationService.createFormulations.mockResolvedValue(expectedResult);
      const result = await controller.createFormulations(dto as never, mockRequest);
      expect(mockFormulationService.createFormulations).toHaveBeenCalledWith(dto, "header-user-1");
      expect(result).toEqual(expectedResult);
    });
  });

  describe("compareFormulations", () => {
    it("should compare formulations with materialCriteria filter for ADMIN", async () => {
      const request: ComparisonRequestDto = {
        limit: 10,
        materialCriteria: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            maxValue: 80,
            minValue: 10,
          },
        ],
        page: 1,
      };

      const mockRequest = {
        user: {
          oid: "admin-user-id",
          role: "ADMIN",
        },
      } as AuthenticatedRequest;

      const expectedResult: ComparisonResponseDto = {
        data: [
          {
            formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
            grade: "A",
            isAccessible: true,
            matchingMaterialsCount: 1,
            materials: [
              {
                family: "POLYMERS",
                id: "94f114ca-6504-488f-9083-444b59b6177a",
                origin: "Asia",
                reference: "PP-H 1200",
                searched: true,
                status: "AVAILABLE",
                supplier: "Supplier A",
                supplierBatchNumber: "Batch123",
                type: "Virgin PP Homopolymer",
                value: 70,
              },
              {
                family: "POLYMERS",
                id: "another-uuid",
                origin: "Europe",
                reference: "PlastiLoop",
                searched: true,
                status: "AVAILABLE",
                supplier: "Supplier B",
                supplierBatchNumber: "Batch456",
                type: "Recycle PP Homopolymer",
                value: 30,
              },
            ],
            name: "Bumper",
            optionalCriteriaMatched: 0,
            testResults: [
              {
                condition: "23°C, 50% RH",
                id: "test-result-1",
                maxRange: 30,
                minRange: 20,
                standard: "ASTM D638",
                testName: "Tensile Strength",
                value: 25.5,
              },
            ],
            tier: 1,
            totalOptionalCriteria: 0,
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockComparisonService.getComparisons.mockResolvedValue(expectedResult);

      const result = await controller.compareFormulations(request, mockRequest);

      expect(mockComparisonService.getComparisons).toHaveBeenCalledWith(request, {
        userId: "admin-user-id",
        userRole: "ADMIN",
      });
      expect(mockComparisonService.getComparisons).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });

    it("should compare formulations with ownership filtering for ENGINEERS", async () => {
      const request: ComparisonRequestDto = {
        limit: 10,
        materialCriteria: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            minValue: 10,
          },
        ],
        page: 1,
      };

      const mockRequest = {
        user: {
          oid: "engineer-user-id",
          role: "ENGINEERS",
        },
      } as AuthenticatedRequest;

      const expectedResult: ComparisonResponseDto = {
        data: [
          {
            formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
            grade: "A",
            isAccessible: false,
            matchingMaterialsCount: 0,
            materials: [], // Empty for non-owned formulation
            name: "Bumper",
            optionalCriteriaMatched: 0,
            testResults: [],
            tier: 1,
            totalOptionalCriteria: 0,
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockComparisonService.getComparisons.mockResolvedValue(expectedResult);

      const result = await controller.compareFormulations(request, mockRequest);

      expect(mockComparisonService.getComparisons).toHaveBeenCalledWith(request, {
        userId: "engineer-user-id",
        userRole: "ENGINEERS",
      });
      expect(mockComparisonService.getComparisons).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });

    it("should handle request without materialCriteria", async () => {
      const request: ComparisonRequestDto = {
        limit: 5,
        page: 1,
      };

      const mockRequest = {
        user: {
          oid: "data-scientist-id",
          role: "DATA_SCIENTIST",
        },
      } as AuthenticatedRequest;

      const expectedResult: ComparisonResponseDto = {
        data: [],
        meta: {
          currentPage: 1,
          from: 0,
          lastPage: 1,
          perPage: 5,
          to: 0,
          total: 0,
        },
      };

      mockComparisonService.getComparisons.mockResolvedValue(expectedResult);

      const result = await controller.compareFormulations(request, mockRequest);

      expect(mockComparisonService.getComparisons).toHaveBeenCalledWith(request, {
        userId: "data-scientist-id",
        userRole: "DATA_SCIENTIST",
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle empty request for ENGINEERS", async () => {
      const request: ComparisonRequestDto = {};
      const mockRequest = {
        user: {
          oid: "engineer-id",
          role: "ENGINEERS",
        },
      } as AuthenticatedRequest;

      const expectedResult: ComparisonResponseDto = {
        data: [],
        meta: {
          currentPage: 1,
          from: 0,
          lastPage: 1,
          perPage: 10,
          to: 0,
          total: 0,
        },
      };

      mockComparisonService.getComparisons.mockResolvedValue(expectedResult);

      const result = await controller.compareFormulations(request, mockRequest);

      expect(mockComparisonService.getComparisons).toHaveBeenCalledWith(request, {
        userId: "engineer-id",
        userRole: "ENGINEERS",
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe("compareFormulations edge cases", () => {
    it("should map criteria.valueTo to value array", async () => {
      const request: unknown = { criteria: [{ value: 10, valueTo: 20 }] };
      const mockRequest = { user: { oid: "admin-1", role: "ADMIN" } } as AuthenticatedRequest;
      const expectedResult = { data: [] };
      mockComparisonService.getComparisons.mockResolvedValue(expectedResult);
      await controller.compareFormulations(request as never, mockRequest);
      expect((request as { criteria: { value: number[] }[] }).criteria[0].value).toEqual([10, 20]);
    });
    it("should handle request without user (undefined)", async () => {
      const request: unknown = { criteria: [] };
      await expect(controller.compareFormulations(request as never, {} as unknown as AuthenticatedRequest)).rejects.toThrow("User ID or role not found in request");
    });
  });

  describe("getFormulationById", () => {
    it("should get formulation by ID for ADMIN user", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const mockRequest = {
        user: {
          oid: "admin-user-id",
          role: "ADMIN",
        },
      } as AuthenticatedRequest;

      const expectedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        grade: "A",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
          {
            materialId: "another-uuid",
            materialType: "Recycle PP Homopolymer",
            reference: "PlastiLoop",
            value: 30,
          },
        ],
        name: "Bumper",
        ownerId: "owner-user-id",
        testResults: [
          {
            condition: "23°C, 50% RH",
            id: "test-result-1",
            maxRange: 30,
            minRange: 20,
            standard: "ASTM D638",
            testName: "Tensile Strength",
            value: 25.5,
          },
        ],
      };

      mockFormulationService.getFormulationById.mockResolvedValue(expectedResult);

      const result = await controller.getFormulationById(formulationId, mockRequest);

      expect(mockFormulationService.getFormulationById).toHaveBeenCalledWith(
        formulationId,
        "ADMIN",
        "admin-user-id"
      );
      expect(mockFormulationService.getFormulationById).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });

    it("should get formulation by ID for ENGINEERS user with ownership", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const mockRequest = {
        user: {
          oid: "engineer-user-id",
          role: "ENGINEERS",
        },
      } as AuthenticatedRequest;

      const expectedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        grade: "A",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
        ],
        name: "Bumper",
        ownerId: "engineer-user-id",
        testResults: [
          {
            condition: "23°C, 50% RH",
            id: "test-result-1",
            maxRange: 30,
            minRange: 20,
            standard: "ASTM D638",
            testName: "Tensile Strength",
            value: 25.5,
          },
        ],
      };

      mockFormulationService.getFormulationById.mockResolvedValue(expectedResult);

      const result = await controller.getFormulationById(formulationId, mockRequest);

      expect(mockFormulationService.getFormulationById).toHaveBeenCalledWith(
        formulationId,
        "ENGINEERS",
        "engineer-user-id"
      );
      expect(result).toEqual(expectedResult);
    });

    it("should get formulation by ID for ENGINEERS user with approved request", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const mockRequest = {
        user: {
          oid: "engineer-user-id",
          role: "ENGINEERS",
        },
      } as AuthenticatedRequest;

      const expectedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        grade: "A",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
        ],
        name: "Bumper",
        ownerId: "other-user-id",
        testResults: [
          {
            condition: "23°C, 50% RH",
            id: "test-result-1",
            maxRange: 30,
            minRange: 20,
            standard: "ASTM D638",
            testName: "Tensile Strength",
            value: 25.5,
          },
        ],
      };

      mockFormulationService.getFormulationById.mockResolvedValue(expectedResult);

      const result = await controller.getFormulationById(formulationId, mockRequest);

      expect(mockFormulationService.getFormulationById).toHaveBeenCalledWith(
        formulationId,
        "ENGINEERS",
        "engineer-user-id"
      );
      expect(result).toEqual(expectedResult);
    });

    it("should get formulation by ID for ENGINEERS user without access", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const mockRequest = {
        user: {
          oid: "engineer-user-id",
          role: "ENGINEERS",
        },
      } as AuthenticatedRequest;

      const expectedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        grade: "A",
        isAccessible: false,
        materials: [],
        name: "Bumper",
        ownerId: "other-user-id",
        testResults: [],
      };

      mockFormulationService.getFormulationById.mockResolvedValue(expectedResult);

      const result = await controller.getFormulationById(formulationId, mockRequest);

      expect(mockFormulationService.getFormulationById).toHaveBeenCalledWith(
        formulationId,
        "ENGINEERS",
        "engineer-user-id"
      );
      expect(result).toEqual(expectedResult);
    });

    it("should throw NotFoundException when formulation not found", async () => {
      const formulationId = "non-existent-id";
      const mockRequest = {
        user: {
          oid: "admin-user-id",
          role: "ADMIN",
        },
      } as AuthenticatedRequest;

      mockFormulationService.getFormulationById.mockRejectedValue(new Error(`Formulation with ID ${formulationId} not found`));

      await expect(controller.getFormulationById(formulationId, mockRequest)).rejects.toThrow(
        `Formulation with ID ${formulationId} not found`
      );

      expect(mockFormulationService.getFormulationById).toHaveBeenCalledWith(
        formulationId,
        "ADMIN",
        "admin-user-id"
      );
    });

    it("should handle request without user information", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const mockRequest = {} as AuthenticatedRequest;

      const expectedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        grade: "A",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
        ],
        name: "Bumper",
        ownerId: "owner-user-id",
        testResults: [
          {
            condition: "23°C, 50% RH",
            id: "test-result-1",
            maxRange: 30,
            minRange: 20,
            standard: "ASTM D638",
            testName: "Tensile Strength",
            value: 25.5,
          },
        ],
      };

      mockFormulationService.getFormulationById.mockResolvedValue(expectedResult);

      const result = await controller.getFormulationById(formulationId, mockRequest);

      expect(mockFormulationService.getFormulationById).toHaveBeenCalledWith(
        formulationId,
        undefined,
        undefined
      );
      expect(result).toEqual(expectedResult);
    });
  });

  describe("getFormulations", () => {
    it("should return paginated formulations with all query parameters", async () => {
      const expectedResult = {
        data: [
          {
            formulationId: "form-1",
            grade: "A",
            isAccessible: true,
            materials: [
              {
                materialId: "mat-1",
                materialType: "Virgin PP Homopolymer",
                reference: "PP-H 1200",
                value: 70,
              },
            ],
            name: "Bumper",
            ownerId: "owner-1",
            testResults: [
              {
                condition: "23°C, 50% RH",
                id: "test-result-1",
                maxRange: 30,
                minRange: 20,
                standard: "ASTM D638",
                testName: "Tensile Strength",
                value: 25.5,
              },
            ],
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockFormulationService.getFormulations.mockResolvedValue(expectedResult);

      const filter = { limit: 10, page: 1, search: "Bumper" };
      const mockRequest = { user: { oid: "owner-1", role: "ADMIN" } } as AuthenticatedRequest;
      const result = await controller.getFormulations(filter, mockRequest);

      expect(mockFormulationService.getFormulations).toHaveBeenCalledWith(filter, "ADMIN", "owner-1");
      expect(result).toEqual(expectedResult);
    });

    it("should return paginated formulations with no filters", async () => {
      const expectedResult = {
        data: [],
        meta: {
          currentPage: 1,
          from: 0,
          lastPage: 1,
          perPage: 10,
          to: 0,
          total: 0,
        },
      };
      mockFormulationService.getFormulations.mockResolvedValue(expectedResult);
      const filter = {};
      const mockRequest = { user: { oid: "engineer-1", role: "ENGINEERS" } } as AuthenticatedRequest;
      const result = await controller.getFormulations(filter, mockRequest);
      expect(mockFormulationService.getFormulations).toHaveBeenCalledWith(filter, "ENGINEERS", "engineer-1");
      expect(result).toEqual(expectedResult);
    });

    it("should handle filter by ownerId and grade", async () => {
      const expectedResult = {
        data: [
          {
            formulationId: "form-2",
            grade: "B",
            isAccessible: true,
            materials: [],
            name: "Dashboard",
            ownerId: "owner-2",
            testResults: [],
          },
        ],
        meta: {
          currentPage: 2,
          from: 6,
          lastPage: 1,
          perPage: 5,
          to: 10,
          total: 1,
        },
      };
      mockFormulationService.getFormulations.mockResolvedValue(expectedResult);
      const filter = { grade: "B", limit: 5, ownerId: "owner-2", page: 2 };
      const mockRequest = { user: { oid: "ds-1", role: "DATA_SCIENTIST" } } as AuthenticatedRequest;
      const result = await controller.getFormulations(filter, mockRequest);
      expect(mockFormulationService.getFormulations).toHaveBeenCalledWith(filter, "DATA_SCIENTIST", "ds-1");
      expect(result).toEqual(expectedResult);
    });

    it("should handle undefined request user", async () => {
      const expectedResult = {
        data: [],
        meta: {
          currentPage: 1,
          from: 0,
          lastPage: 1,
          perPage: 10,
          to: 0,
          total: 0,
        },
      };
      mockFormulationService.getFormulations.mockResolvedValue(expectedResult);
      const filter = {};
      const result = await controller.getFormulations(filter);
      expect(mockFormulationService.getFormulations).toHaveBeenCalledWith(filter, undefined, undefined);
      expect(result).toEqual(expectedResult);
    });
  });
});
