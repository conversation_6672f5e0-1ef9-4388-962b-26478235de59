import { Injectable } from "@nestjs/common";
import { createPaginatedResponse, normalizePaginationParameters } from "../common/utils/pagination.utility";
import { ComparisonRequestDto, ComparisonResponseDto } from "./dto";
import { ComparisonRepository } from "./repositories/comparison.repository";

export interface UserContext {
  userId: string
  userRole: string
}

@Injectable()
export class ComparisonService {
  public constructor(
    private readonly comparisonRepository: ComparisonRepository,
  ) {}

  public async getComparisons(
    request: ComparisonRequestDto,
    userContext?: UserContext,
  ): Promise<ComparisonResponseDto> {
    const { criteria, materialCriteria } = request;
    const { limit, page } = normalizePaginationParameters(request.page, request.limit);

    const { formulations, total }
      = await this.comparisonRepository.getFormulations(
        page,
        limit,
        userContext?.userId,
        criteria,
        materialCriteria,
        userContext?.userRole,
      );

    return createPaginatedResponse({
      data: formulations,
      limit,
      page,
      total,
    });
  }
}
