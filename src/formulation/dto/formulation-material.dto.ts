import { ApiProperty } from "@nestjs/swagger";

export class FormulationMaterialDto {
  @ApiProperty({
    description: "Material ID",
    example: "94f114ca-6504-488f-9083-444b59b6177a",
  })
  public id: string;

  @ApiProperty({
    description: "Material type name",
    example: "Virgin PP Homopolymer",
  })
  public type: string;

  @ApiProperty({
    description: "Material reference code",
    example: "PP-H 1200",
  })
  public reference: string;

  @ApiProperty({
    description: "Material family",
    example: "POLYMERS",
  })
  public family: string;

  @ApiProperty({
    description: "Material status",
    example: "AVAILABLE",
  })
  public status: string;

  @ApiProperty({
    description: "Material supplier",
    example: "Supplier A",
  })
  public supplier: string;

  @ApiProperty({
    description: "Material supplier batch number",
    example: "Batch123",
  })
  public supplierBatchNumber: string;

  @ApiProperty({
    description: "Material origin",
    example: "Asia",
  })
  public origin: string;

  @ApiProperty({
    description: "Material percentage value in the formulation",
    example: 70,
  })
  public value: number;
}
