import { EmailClient, EmailMessage } from "@azure/communication-email";
import { Inject, Injectable, Logger } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { NewFormulationRequestEvent } from "../request/events/new-formulation-request.event";
import { EMAIL_ENABLED_TOKEN } from "./email.constants";

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly emailClient: EmailClient;
  private readonly senderAddress: string;
  private readonly isEmailEnabled: boolean;

  public constructor(
    @Inject(EMAIL_ENABLED_TOKEN) isEmailEnabled: boolean
  ) {
    const connectionString = process.env.AZURE_COMMUNICATION_SERVICE_CONNECTION_STRING ?? "<your-acs-connection-string>";
    this.senderAddress = process.env.ACS_SENDER_EMAIL ?? "<your-sender-email>";
    this.emailClient = new EmailClient(connectionString);
    this.isEmailEnabled = isEmailEnabled;
  }

  /**
   * Send an email to a list of users, similar to createNotification.
   * @param parameters - The parameters object
   * @param parameters.emails - List of email addresses to send the notification to.
   * @param parameters.title - The title of the email.
   * @param parameters.message - The message content of the email.
   * @param parameters.html - Optional HTML content for the email.
   */
  public async send(parameters: { emails: string[], html?: string, message: string, title: string }): Promise<void> {
    const { emails, html, message, title } = parameters;

    if (emails.length === 0) return;

    if (!this.isEmailEnabled) {
      return;
    }
    const htmlContent = html ?? `<html><body><h2>${title}</h2><p>${message}</p></body></html>`;
    const emailMessage: EmailMessage = {
      content: {
        html: htmlContent,
        subject: title,
      },
      recipients: {
        to: emails.map(email => ({ address: email })),
      },
      senderAddress: this.senderAddress,
    };
    await this.emailClient.beginSend(emailMessage);
  }

  @OnEvent("request.new-formulation")
  public async handleNewFormulationRequest(event: NewFormulationRequestEvent) {
    if (!this.isEmailEnabled) return;
    if (!event.managerEmails || event.managerEmails.length === 0) return;

    await this.send({
      emails: event.managerEmails,
      html: event.html,
      message: event.message,
      title: "New Formulation Request Submitted",
    });
  }
}
