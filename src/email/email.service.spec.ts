import { Test, TestingModule } from "@nestjs/testing";
import { EMAIL_ENABLED_TOKEN } from "./email.constants";
import { EmailService } from "./email.service";

const mockBeginSend = jest.fn();

// Mock the EmailClient before using it
jest.mock("@azure/communication-email", () => ({
  EmailClient: jest.fn().mockImplementation(() => ({
    beginSend: mockBeginSend,
  })),
}));

describe("EmailService", () => {
  let service: EmailService;

  const createTestingModule = async (isEmailEnabled = true) => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: EMAIL_ENABLED_TOKEN,
          useValue: isEmailEnabled,
        },
      ],
    }).compile();

    return module;
  };

  beforeEach(async () => {
    process.env.ACS_SENDER_EMAIL = "<your-sender-email>";
    const module = await createTestingModule(true);
    service = module.get<EmailService>(EmailService);
    mockBeginSend.mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  it("should send email with provided parameters", async () => {
    const parameters = {
      emails: ["<EMAIL>", "<EMAIL>"],
      html: "<html><body><h1>Custom HTML</h1></body></html>",
      message: "Test message content",
      title: "Test Subject",
    };

    mockBeginSend.mockResolvedValue({ messageId: "test-id" });

    await service.send(parameters);

    expect(mockBeginSend).toHaveBeenCalledWith({
      content: {
        html: "<html><body><h1>Custom HTML</h1></body></html>",
        subject: "Test Subject",
      },
      recipients: {
        to: [
          { address: "<EMAIL>" },
          { address: "<EMAIL>" },
        ],
      },
      senderAddress: "<your-sender-email>",
    });
  });

  it("should use default HTML template when html is not provided", async () => {
    const parameters = {
      emails: ["<EMAIL>"],
      message: "Test message",
      title: "Test Subject",
    };

    mockBeginSend.mockResolvedValue({ messageId: "test-id" });

    await service.send(parameters);

    expect(mockBeginSend).toHaveBeenCalledWith({
      content: {
        html: "<html><body><h2>Test Subject</h2><p>Test message</p></body></html>",
        subject: "Test Subject",
      },
      recipients: {
        to: [{ address: "<EMAIL>" }],
      },
      senderAddress: "<your-sender-email>",
    });
  });

  it("should not send email when emails array is empty", async () => {
    const parameters = {
      emails: [],
      message: "Test message",
      title: "Test Subject",
    };

    await service.send(parameters);

    expect(mockBeginSend).not.toHaveBeenCalled();
  });

  it("should handle single email address", async () => {
    const parameters = {
      emails: ["<EMAIL>"],
      message: "Single email message",
      title: "Single Email Test",
    };

    mockBeginSend.mockResolvedValue({ messageId: "test-id" });

    await service.send(parameters);

    expect(mockBeginSend).toHaveBeenCalledWith({
      content: {
        html: "<html><body><h2>Single Email Test</h2><p>Single email message</p></body></html>",
        subject: "Single Email Test",
      },
      recipients: {
        to: [{ address: "<EMAIL>" }],
      },
      senderAddress: "<your-sender-email>",
    });
  });

  it("should handle emails with special characters", async () => {
    const parameters = {
      emails: ["<EMAIL>"],
      message: "Message with special chars: & < > \"",
      title: "Subject with émojis 🎉",
    };

    mockBeginSend.mockResolvedValue({ messageId: "test-id" });

    await service.send(parameters);

    expect(mockBeginSend).toHaveBeenCalledWith(
      expect.objectContaining({
        content: {
          html: "<html><body><h2>Subject with émojis 🎉</h2><p>Message with special chars: & < > \"</p></body></html>",
          subject: "Subject with émojis 🎉",
        },
        recipients: {
          to: [{ address: "<EMAIL>" }],
        },
      })
    );
  });

  it("should propagate errors from EmailClient", async () => {
    const parameters = {
      emails: ["<EMAIL>"],
      message: "Test message",
      title: "Test Subject",
    };

    const error = new Error("EmailClient error");
    mockBeginSend.mockRejectedValue(error);

    await expect(service.send(parameters)).rejects.toThrow("EmailClient error");
  });

  describe("Feature Flag Tests", () => {
    it("should handle NewFormulationRequestEvent and call send", async () => {
      const spy = jest.spyOn(service, "send").mockResolvedValue();
      const event = {
        formulationName: "FormA",
        html: "<html><body>Test</body></html>",
        managerEmails: ["<EMAIL>"],
        managerIds: ["manager1"],
        message: "Test message",
        requesterId: "user1",
      };

      await service.handleNewFormulationRequest(event);
      expect(spy).toHaveBeenCalledWith({
        emails: ["<EMAIL>"],
        html: "<html><body>Test</body></html>",
        message: "Test message",
        title: "New Formulation Request Submitted",
      });
    });

    it("should not send email when feature flag is disabled", async () => {
      const module = await createTestingModule(false);
      const disabledService = module.get<EmailService>(EmailService);

      const parameters = {
        emails: ["<EMAIL>"],
        message: "Test message",
        title: "Test Subject",
      };

      await disabledService.send(parameters);

      expect(mockBeginSend).not.toHaveBeenCalled();
    });

    it("should send email when feature flag is enabled", async () => {
      const parameters = {
        emails: ["<EMAIL>"],
        message: "Test message",
        title: "Test Subject",
      };

      mockBeginSend.mockResolvedValue({ messageId: "test-id" });

      await service.send(parameters);

      expect(mockBeginSend).toHaveBeenCalled();
    });
  });
});
