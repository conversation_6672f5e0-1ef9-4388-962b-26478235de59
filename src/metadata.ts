/* eslint-disable */
export default async () => {
    const t = {
        ["./common/dto/criteria-operator.dto"]: await import("./common/dto/criteria-operator.dto"),
        ["./formulation/dto/material-criteria.dto"]: await import("./formulation/dto/material-criteria.dto"),
        ["./formulation/dto/criteria.dto"]: await import("./formulation/dto/criteria.dto"),
        ["./common/dto/pagination-meta.dto"]: await import("./common/dto/pagination-meta.dto"),
        ["./formulation/dto/material-in-formulation.dto"]: await import("./formulation/dto/material-in-formulation.dto"),
        ["./formulation/dto/test-result.dto"]: await import("./formulation/dto/test-result.dto"),
        ["./formulation/dto/formulation-comparison.dto"]: await import("./formulation/dto/formulation-comparison.dto"),
        ["./formulation/dto/created-formulation.dto"]: await import("./formulation/dto/created-formulation.dto"),
        ["./formulation/dto/create-formulation-material.dto"]: await import("./formulation/dto/create-formulation-material.dto"),
        ["./formulation/dto/create-formulation-test-result.dto"]: await import("./formulation/dto/create-formulation-test-result.dto"),
        ["./formulation/dto/simulation/request-simulation-grade.dto"]: await import("./formulation/dto/simulation/request-simulation-grade.dto"),
        ["./formulation/dto/simulation/response-simulation-test-grade.dto"]: await import("./formulation/dto/simulation/response-simulation-test-grade.dto"),
        ["./formulation/dto/simulation/response-simulation-material.dto"]: await import("./formulation/dto/simulation/response-simulation-material.dto"),
        ["./formulation/dto/simulation/response-simulation-test-result.dto"]: await import("./formulation/dto/simulation/response-simulation-test-result.dto"),
        ["./formulation/dto/simulation/request-simulation-material.dto"]: await import("./formulation/dto/simulation/request-simulation-material.dto"),
        ["./formulation/dto/formulation-material.dto"]: await import("./formulation/dto/formulation-material.dto"),
        ["./formulation/dto/formulation-test-result.dto"]: await import("./formulation/dto/formulation-test-result.dto"),
        ["./formulation/dto/formulation-response.dto"]: await import("./formulation/dto/formulation-response.dto"),
        ["./role/role.types"]: await import("./role/role.types"),
        ["./user/dto/user-response.dto"]: await import("./user/dto/user-response.dto"),
        ["./material/dto/material-search-response.dto"]: await import("./material/dto/material-search-response.dto"),
        ["./material/dto/material-comparison.dto"]: await import("./material/dto/material-comparison.dto"),
        ["./material/enum/material-filter-column.enum"]: await import("./material/enum/material-filter-column.enum"),
        ["./material/dto/family-criteria.dto"]: await import("./material/dto/family-criteria.dto"),
        ["./material/dto/material-page-response.dto"]: await import("./material/dto/material-page-response.dto"),
        ["./notification/dto/notification-response.dto"]: await import("./notification/dto/notification-response.dto"),
        ["./request/dto/location.dto"]: await import("./request/dto/location.dto"),
        ["./request/dto/department.dto"]: await import("./request/dto/department.dto"),
        ["./request/dto/requester.dto"]: await import("./request/dto/requester.dto"),
        ["./request/dto/formulation.dto"]: await import("./request/dto/formulation.dto"),
        ["./request/dto/request-response.dto"]: await import("./request/dto/request-response.dto"),
        ["./request/dto/request-action.enum"]: await import("./request/dto/request-action.enum"),
        ["./specification/dto/specification-response.dto"]: await import("./specification/dto/specification-response.dto"),
        ["./material/dto/paginated-material-response.dto"]: await import("./material/dto/paginated-material-response.dto"),
        ["./material/dto/material-filter-response.dto"]: await import("./material/dto/material-filter-response.dto"),
        ["./material/dto/paginated-material-page-response.dto"]: await import("./material/dto/paginated-material-page-response.dto"),
        ["./material/dto/material-comparison-response.dto"]: await import("./material/dto/material-comparison-response.dto"),
        ["./material/dto/batch-update-material-response.dto"]: await import("./material/dto/batch-update-material-response.dto"),
        ["./formulation/dto/create-formulation-response.dto"]: await import("./formulation/dto/create-formulation-response.dto"),
        ["./formulation/dto/paginated-formulation-response.dto"]: await import("./formulation/dto/paginated-formulation-response.dto"),
        ["./formulation/dto/comparison-response.dto"]: await import("./formulation/dto/comparison-response.dto"),
        ["./formulation/dto/create-simulation-response.dto"]: await import("./formulation/dto/create-simulation-response.dto"),
        ["./notification/dto/paginated-notification-response.dto"]: await import("./notification/dto/paginated-notification-response.dto"),
        ["./user/dto/paginated-user-response.dto"]: await import("./user/dto/paginated-user-response.dto"),
        ["./request/dto/paginated-request-response.dto"]: await import("./request/dto/paginated-request-response.dto"),
        ["./specification/dto/paginated-specification-response.dto"]: await import("./specification/dto/paginated-specification-response.dto")
    };
    return { "@nestjs/swagger": { "models": [[import("./department/dto/department-response.dto"), { "DepartmentResponseDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String } } }], [import("./common/dto/pagination-meta.dto"), { "PaginationMetaDto": { total: { required: true, type: () => Number }, perPage: { required: true, type: () => Number }, currentPage: { required: true, type: () => Number }, lastPage: { required: true, type: () => Number }, from: { required: true, type: () => Number }, to: { required: true, type: () => Number } } }], [import("./formulation/dto/criteria.dto"), { "CriteriaDto": { propertyName: { required: true, type: () => String }, tier: { required: false, type: () => Number }, operator: { required: true, enum: t["./common/dto/criteria-operator.dto"].CRITERIA_OPERATOR }, value: { required: true, type: () => Object }, valueTo: { required: true, type: () => Number }, required: { required: true, type: () => Boolean } } }], [import("./formulation/dto/material-criteria.dto"), { "MaterialCriteriaDto": { materialId: { required: true, type: () => String }, minValue: { required: false, type: () => Number }, maxValue: { required: false, type: () => Number } } }], [import("./formulation/dto/comparison-request.dto"), { "ComparisonRequestDto": { materialCriteria: { required: false, type: () => [t["./formulation/dto/material-criteria.dto"].MaterialCriteriaDto] }, page: { required: false, type: () => Number, default: 1, minimum: 1 }, limit: { required: false, type: () => Number, default: 10, minimum: 1 }, criteria: { required: false, type: () => [t["./formulation/dto/criteria.dto"].CriteriaDto] } } }], [import("./common/dto/error-response.dto"), { "ErrorResponseDto": { message: { required: true, type: () => String }, code: { required: true, type: () => String } } }], [import("./common/dto/pagination-query.dto"), { "PaginationQueryDto": { page: { required: false, type: () => Number, default: 1, minimum: 1 }, limit: { required: false, type: () => Number, default: 10, minimum: 1 } } }], [import("./common/dto/paginated-response.dto"), { "PaginatedResponseDto": { data: { required: true }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./common/dto/user.dto"), { "UserDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String } } }], [import("./formulation/dto/formulation-material.dto"), { "FormulationMaterialDto": { id: { required: true, type: () => String }, type: { required: true, type: () => String }, reference: { required: true, type: () => String }, family: { required: true, type: () => String }, status: { required: true, type: () => String }, supplier: { required: true, type: () => String }, supplierBatchNumber: { required: true, type: () => String }, origin: { required: true, type: () => String }, value: { required: true, type: () => Number } } }], [import("./formulation/dto/material-in-formulation.dto"), { "MaterialInFormulationDto": { searched: { required: true, type: () => Boolean } } }], [import("./formulation/dto/test-result.dto"), { "TestResultDto": { id: { required: true, type: () => String }, testName: { required: true, type: () => String }, standard: { required: true, type: () => String, nullable: true }, condition: { required: true, type: () => String, nullable: true }, value: { required: true, type: () => Number }, minRange: { required: false, type: () => Number, nullable: true }, maxRange: { required: false, type: () => Number, nullable: true } } }], [import("./formulation/dto/formulation-comparison.dto"), { "FormulationComparisonDto": { formulationId: { required: true, type: () => String }, name: { required: true, type: () => String }, grade: { required: true, type: () => String }, isAccessible: { required: true, type: () => Boolean }, materials: { required: true, type: () => [t["./formulation/dto/material-in-formulation.dto"].MaterialInFormulationDto] }, matchingMaterialsCount: { required: true, type: () => Number }, testResults: { required: true, type: () => [t["./formulation/dto/test-result.dto"].TestResultDto] }, tier: { required: false, type: () => Number }, optionalCriteriaMatched: { required: true, type: () => Number }, totalOptionalCriteria: { required: true, type: () => Number } } }], [import("./formulation/dto/comparison-response.dto"), { "ComparisonResponseDto": { data: { required: true, type: () => [t["./formulation/dto/formulation-comparison.dto"].FormulationComparisonDto] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./formulation/dto/created-formulation.dto"), { "CreatedFormulationDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String }, grade: { required: true, type: () => String }, ownerId: { required: true, type: () => String } } }], [import("./formulation/dto/create-formulation-response.dto"), { "CreateFormulationResponseDto": { data: { required: true, type: () => [t["./formulation/dto/created-formulation.dto"].CreatedFormulationDto] } } }], [import("./formulation/dto/create-formulation-material.dto"), { "CreateFormulationMaterialDto": { materialId: { required: true, type: () => String, format: "uuid" }, grades: { required: true, type: () => Object } } }], [import("./formulation/dto/test-result-grade-value.dto"), { "TestResultGradeValueDto": { value: { required: true, type: () => Number }, minRange: { required: false, type: () => Number }, maxRange: { required: false, type: () => Number } } }], [import("./formulation/dto/create-formulation-test-result.dto"), { "CreateFormulationTestResultDto": { testName: { required: true, type: () => String }, standard: { required: false, type: () => String }, condition: { required: false, type: () => String }, propertyName: { required: true, type: () => String }, grades: { required: true, type: () => Object } } }], [import("./formulation/dto/create-formulation.dto"), { "CreateFormulationDto": { code: { required: true, type: () => String }, name: { required: true, type: () => String }, materials: { required: true, type: () => [t["./formulation/dto/create-formulation-material.dto"].CreateFormulationMaterialDto] }, testResults: { required: true, type: () => [t["./formulation/dto/create-formulation-test-result.dto"].CreateFormulationTestResultDto] } } }], [import("./formulation/dto/simulation/request-simulation-grade.dto"), { "RequestSimulationGradeDto": { formulationId: { required: true, type: () => String }, code: { required: true, type: () => String }, grade: { required: true, type: () => String }, value: { required: true, type: () => Number } } }], [import("./formulation/dto/simulation/base-simulation-material.dto"), { "BaseSimulationMaterialDto": { materialId: { required: true, type: () => String }, grades: { required: true, type: () => [t["./formulation/dto/simulation/request-simulation-grade.dto"].RequestSimulationGradeDto] } } }], [import("./formulation/dto/simulation/response-simulation-material.dto"), { "ResponseSimulationMaterialDto": { materialType: { required: false, type: () => String }, reference: { required: false, type: () => String } } }], [import("./formulation/dto/simulation/response-simulation-test-grade.dto"), { "ResponseSimulationTestGradeDto": { grade: { required: true, type: () => String }, value: { required: true, type: () => Number }, minRange: { required: true, type: () => Number }, maxRange: { required: true, type: () => Number } } }], [import("./formulation/dto/simulation/response-simulation-test-result.dto"), { "ResponseSimulationTestResultDto": { id: { required: true, type: () => String }, testName: { required: true, type: () => String }, standard: { required: true, type: () => String }, condition: { required: true, type: () => String }, grades: { required: true, type: () => [t["./formulation/dto/simulation/response-simulation-test-grade.dto"].ResponseSimulationTestGradeDto] } } }], [import("./formulation/dto/create-simulation-response.dto"), { "CreateSimulationResponseDto": { materials: { required: true, type: () => [t["./formulation/dto/simulation/response-simulation-material.dto"].ResponseSimulationMaterialDto] }, simulationResults: { required: true, type: () => [t["./formulation/dto/simulation/response-simulation-test-result.dto"].ResponseSimulationTestResultDto] }, real: { required: true, type: () => [t["./formulation/dto/simulation/response-simulation-test-result.dto"].ResponseSimulationTestResultDto] } } }], [import("./formulation/dto/simulation/request-simulation-material.dto"), { "RequestSimulationMaterialDto": {} }], [import("./formulation/dto/create-simulation.dto"), { "CreateSimulationDto": { materials: { required: true, type: () => [t["./formulation/dto/simulation/request-simulation-material.dto"].RequestSimulationMaterialDto] } } }], [import("./formulation/dto/formulation-filter.dto"), { "FormulationFilterDto": { search: { required: false, type: () => String }, name: { required: false, type: () => String }, grade: { required: false, type: () => String }, ownerId: { required: false, type: () => String }, page: { required: false, type: () => Number, default: 1, minimum: 1 }, limit: { required: false, type: () => Number, default: 10, minimum: 1, maximum: 100 } } }], [import("./formulation/dto/formulation-test-result.dto"), { "FormulationTestResultDto": { id: { required: true, type: () => String }, testName: { required: true, type: () => String }, standard: { required: false, type: () => String }, condition: { required: false, type: () => String }, value: { required: true, type: () => Number }, minRange: { required: false, type: () => Number }, maxRange: { required: false, type: () => Number } } }], [import("./formulation/dto/formulation-response.dto"), { "FormulationResponseDto": { formulationId: { required: true, type: () => String }, name: { required: true, type: () => String }, grade: { required: true, type: () => String }, ownerId: { required: true, type: () => String }, isAccessible: { required: true, type: () => Boolean }, materials: { required: true, type: () => [t["./formulation/dto/formulation-material.dto"].FormulationMaterialDto] }, testResults: { required: true, type: () => [t["./formulation/dto/formulation-test-result.dto"].FormulationTestResultDto] } } }], [import("./formulation/dto/paginated-formulation-response.dto"), { "PaginatedFormulationResponseDto": { data: { required: true, type: () => [t["./formulation/dto/formulation-response.dto"].FormulationResponseDto] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./location/dto/location-response.dto"), { "LocationResponseDto": { id: { required: true, type: () => String }, city: { required: true, type: () => String }, country: { required: true, type: () => String } } }], [import("./role/dto/role-response.dto"), { "RoleResponseDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String } } }], [import("./user/dto/create-user.dto"), { "CreateUserDto": { departmentId: { required: true, type: () => String, format: "uuid" }, email: { required: true, type: () => String, format: "email" }, locationId: { required: true, type: () => String, format: "uuid" }, name: { required: true, type: () => String }, roleId: { required: true, type: () => String, format: "uuid" } } }], [import("./user/dto/update-user.dto"), { "UpdateUserDto": {} }], [import("./user/dto/user-filter.dto"), { "UserFilterDto": { locationId: { required: false, type: () => String, format: "uuid" }, departmentId: { required: false, type: () => String, format: "uuid" }, roleId: { required: false, type: () => String, format: "uuid" }, roleCode: { required: false, enum: t["./role/role.types"].USER_ROLE }, search: { required: false, type: () => String } } }], [import("./user/dto/user-response.dto"), { "UserResponseDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String }, email: { required: true, type: () => String }, location: { required: true, type: () => String }, department: { required: true, type: () => String }, role: { required: true, type: () => String } } }], [import("./user/dto/paginated-user-response.dto"), { "PaginatedUserResponseDto": { data: { required: true, type: () => [t["./user/dto/user-response.dto"].UserResponseDto] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./material/dto/batch-update-material-request.dto"), { "BatchUpdateMaterialRequestDto": { id: { required: true, type: () => String }, type: { required: false, type: () => String }, reference: { required: false, type: () => String }, family: { required: true, type: () => Object }, supplier: { required: false, type: () => String }, supplierId: { required: false, type: () => String }, supplierBatchNumber: { required: true, type: () => String }, origin: { required: false, type: () => String }, status: { required: false, type: () => Object }, familyData: { required: false, type: () => Object } } }], [import("./material/dto/material-search-response.dto"), { "MaterialSearchResponseDto": { id: { required: true, type: () => String }, type: { required: true, type: () => String }, reference: { required: true, type: () => String }, family: { required: true, type: () => String }, status: { required: true, type: () => Object }, supplier: { required: true, type: () => String, nullable: true }, origin: { required: true, type: () => String, nullable: true }, supplierBatchNumber: { required: true, type: () => String, nullable: true } } }], [import("./material/dto/batch-update-material-response.dto"), { "BatchUpdateMaterialResponseDto": { id: { required: true, type: () => String }, status: { required: true, type: () => Object }, value: { required: false, type: () => t["./material/dto/material-search-response.dto"].MaterialSearchResponseDto }, reason: { required: false, type: () => String } } }], [import("./material/dto/family-criteria.dto"), { "FamilyCriteriaDto": { propertyName: { required: true, type: () => String }, operator: { required: true, enum: t["./common/dto/criteria-operator.dto"].CRITERIA_OPERATOR }, value: { required: false, type: () => Object }, minValue: { required: false, type: () => Number }, maxValue: { required: false, type: () => Number } } }], [import("./material/dto/material-comparison-request.dto"), { "MaterialComparisonRequestDto": { materialIds: { required: true, type: () => [String], format: "uuid" }, includeProperties: { required: false, type: () => [String] } } }], [import("./material/dto/material-comparison.dto"), { "MaterialComparisonDto": { id: { required: true, type: () => String }, reference: { required: true, type: () => String }, type: { required: true, type: () => String }, family: { required: true, type: () => Object }, status: { required: true, type: () => Object }, supplierName: { required: true, type: () => String }, supplierId: { required: true, type: () => String }, supplierBatchNumber: { required: true, type: () => String }, origin: { required: true, type: () => String }, properties: { required: true, type: () => Object } } }], [import("./material/dto/material-comparison-response.dto"), { "MaterialComparisonResponseDto": { materials: { required: true, type: () => [t["./material/dto/material-comparison.dto"].MaterialComparisonDto] }, availableProperties: { required: true, type: () => [String] }, propertyMetadata: { required: true, type: () => Object } } }], [import("./material/dto/material-filter-request.dto"), { "MaterialFilterRequestDto": { column: { required: true, enum: t["./material/enum/material-filter-column.enum"].MATERIAL_FILTER_COLUMN }, page: { required: false, type: () => Number, default: 1, minimum: 1 }, limit: { required: false, type: () => Number, default: 10, minimum: 1, maximum: 100 }, q: { required: false, type: () => String } } }], [import("./material/dto/material-filter-response.dto"), { "MaterialFilterResponseDto": { data: { required: true, type: () => [String] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./material/dto/family-data/additive.dto"), { "AdditiveFamilyDataDto": { id: { required: false, type: () => String, format: "uuid" }, materialId: { required: false, type: () => String, format: "uuid" }, anonymizationCode: { required: false, type: () => String } } }], [import("./material/dto/family-data/elastomer.dto"), { "ElastomerFamilyDataDto": { id: { required: false, type: () => String, format: "uuid" }, materialId: { required: false, type: () => String, format: "uuid" }, density: { required: false, type: () => Number }, mfi190216: { required: false, type: () => Number }, codeanonymElasto: { required: false, type: () => String }, nIzod23: { required: false, type: () => Number }, flexModulus: { required: false, type: () => Number }, tracModulus100: { required: false, type: () => Number }, elongAtBreak: { required: false, type: () => Number }, mfi230216: { required: false, type: () => Number }, meltingPoint: { required: false, type: () => Number }, hdtB: { required: false, type: () => Number }, hdtA: { required: false, type: () => Number }, shoreA: { required: false, type: () => Number }, shoreD: { required: false, type: () => Number } } }], [import("./material/dto/family-data/filler.dto"), { "FillerFamilyDataDto": { id: { required: false, type: () => String, format: "uuid" }, materialId: { required: false, type: () => String, format: "uuid" }, codeanonymFiller: { required: false, type: () => String }, bet: { required: false, type: () => Number }, d50: { required: false, type: () => Number }, d95d05: { required: false, type: () => Number }, whiteness: { required: false, type: () => Number }, form: { required: false, type: () => String } } }], [import("./material/dto/family-data/base-polymer.dto"), { "BasePolymerFamilyDataDto": { id: { required: false, type: () => String, format: "uuid" }, materialId: { required: false, type: () => String, format: "uuid" }, resultUpdateDate: { required: false, type: () => String }, validationEngineeringAndFeedstockOrUseInCompounds: { required: false, type: () => String }, tds: { required: false, type: () => String }, priceExw: { required: false, type: () => Number }, priceExwDate: { required: false, type: () => String }, comment: { required: false, type: () => String }, mfiNorme: { required: false, type: () => String }, mfiTestConditions: { required: false, type: () => String }, mfiAv: { required: false, type: () => Number }, mfiStdDv: { required: false, type: () => Number }, densityNorme: { required: false, type: () => String }, densityAv: { required: false, type: () => Number }, densityStdDv: { required: false, type: () => Number }, tensileModulusNorme: { required: false, type: () => String }, tensileModulusAv: { required: false, type: () => Number }, tensileModulusStdDv: { required: false, type: () => Number }, flexuralModulusNorme: { required: false, type: () => String }, flexuralModulusAv: { required: false, type: () => Number }, flexuralModulusStdDev: { required: false, type: () => Number }, flexuralStressFcAv: { required: false, type: () => Number }, flexuralStressFcStdDev: { required: false, type: () => Number }, stressBreakNorme: { required: false, type: () => String }, stressBreakAv: { required: false, type: () => Number }, stressBreakStdDv: { required: false, type: () => Number }, stressYieldAv: { required: false, type: () => Number }, stressYieldStdDv: { required: false, type: () => Number }, yieldStrainAv: { required: false, type: () => Number }, yieldStrainStdDv: { required: false, type: () => Number }, strainBreakNorme: { required: false, type: () => String }, strainBreakAv: { required: false, type: () => Number }, strainBreakStdDv: { required: false, type: () => Number }, nominalStrainBreakAv: { required: false, type: () => Number }, nominalStrainBreakStdDv: { required: false, type: () => Number }, notchedIzodNorme: { required: false, type: () => String }, notchedIzodAv: { required: false, type: () => Number }, notchedIzodStdDv: { required: false, type: () => Number }, notchedIzodFailureType: { required: false, type: () => String }, unnotchedIzodNorme: { required: false, type: () => String }, unnotchedIzodAv: { required: false, type: () => Number }, unnotchedIzodStdDv: { required: false, type: () => Number }, unnotchedIzodFailureType: { required: false, type: () => String }, hdtBNorme: { required: false, type: () => String }, hdtBAv: { required: false, type: () => Number }, hdtBStdDv: { required: false, type: () => Number }, cma23Norm: { required: false, type: () => String }, cma23V: { required: false, type: () => Number }, cma23NbSamples: { required: false, type: () => Number }, cma23MeanBreakType: { required: false, type: () => String }, cma23EnergyForceMaxAv: { required: false, type: () => Number }, cma23EnergyForceMaxStdDev: { required: false, type: () => Number }, cma23EnergyPunctureAv: { required: false, type: () => Number }, cma23EnergyPunctureStdDev: { required: false, type: () => Number }, cma0Norm: { required: false, type: () => String }, cma0V: { required: false, type: () => Number }, cma0NbSamples: { required: false, type: () => Number }, cma0MeanBreakType: { required: false, type: () => String }, cma0EnergyForceMaxAv: { required: false, type: () => Number }, cma0EnergyForceMaxStdDev: { required: false, type: () => Number }, cma0EnergyPunctureAv: { required: false, type: () => Number }, cma0EnergyPunctureStdDev: { required: false, type: () => Number }, cma10Norm: { required: false, type: () => String }, cma10V: { required: false, type: () => Number }, cma10NbSamples: { required: false, type: () => Number }, cma10MeanBreakType: { required: false, type: () => String }, cma10EnergyForceMaxAv: { required: false, type: () => Number }, cma10EnergyForceMaxStdDev: { required: false, type: () => Number }, cma10EnergyPunctureAv: { required: false, type: () => Number }, cma10EnergyPunctureStdDev: { required: false, type: () => Number }, cma20Norm: { required: false, type: () => String }, cma20V: { required: false, type: () => Number }, cma20NbSamples: { required: false, type: () => Number }, cma20MeanBreakType: { required: false, type: () => String }, cma20EnergyForceMaxAv: { required: false, type: () => Number }, cma20EnergyForceMaxStdDev: { required: false, type: () => Number }, cma20EnergyPunctureAv: { required: false, type: () => Number }, cma20EnergyPunctureStdDev: { required: false, type: () => Number }, cma30Norm: { required: false, type: () => String }, cma30V: { required: false, type: () => Number }, cma30NbSamples: { required: false, type: () => Number }, cma30MeanBreakType: { required: false, type: () => String }, cma30EnergyForceMaxAv: { required: false, type: () => Number }, cma30EnergyForceMaxStdDev: { required: false, type: () => Number }, cma30EnergyPunctureAv: { required: false, type: () => Number }, cma30EnergyPunctureStdDev: { required: false, type: () => Number } } }], [import("./material/dto/family-data/polymer.dto"), { "PolymerFamilyDataDto": { tensileModulusCond: { required: false, type: () => String } } }], [import("./material/dto/family-data/recycle-polymer.dto"), { "RecyclePolymerFamilyDataDto": { technicalProfileAvgValue: { required: false, type: () => String }, color: { required: false, type: () => String }, pirPcrElv: { required: false, type: () => String }, wasteDetails: { required: false, type: () => String }, materialForm: { required: false, type: () => String }, productVolumesKtY: { required: false, type: () => Number }, volumesAvailableForMactKtY: { required: false, type: () => Number }, certificatesReachRohs: { required: false, type: () => String }, endOfWasteStatus: { required: false, type: () => String }, filtrationLocation: { required: false, type: () => String }, systemFiltration: { required: false, type: () => String }, filtrationSize: { required: false, type: () => String }, quantityFilteredRemaining: { required: false, type: () => Number }, d22NbFiltersUsed: { required: false, type: () => Number }, d22NbFilterPerKgFeedstock: { required: false, type: () => Number }, d32QuantityScrap: { required: false, type: () => Number }, d32NbFilterPerKgFeedstock: { required: false, type: () => Number }, levelOfPollution: { required: false, type: () => String }, venting: { required: false, type: () => String }, trialDate: { required: false, type: () => String }, vacuumPressure: { required: false, type: () => String }, screwSpeed: { required: false, type: () => String }, screwProfile: { required: false, type: () => String }, ashContentNorme: { required: false, type: () => String }, ashContentAv: { required: false, type: () => Number }, ashContentStdDv: { required: false, type: () => Number }, odorNorme: { required: false, type: () => String }, odorNote1: { required: false, type: () => String }, odorNote2: { required: false, type: () => String }, odorNote3: { required: false, type: () => String }, odorAv: { required: false, type: () => Number }, odorStdv: { required: false, type: () => Number }, vocFogNorme: { required: false, type: () => String }, voc: { required: false, type: () => Number }, voc2: { required: false, type: () => Number }, fog: { required: false, type: () => Number }, l: { required: false, type: () => Number }, a: { required: false, type: () => Number }, b: { required: false, type: () => Number }, tensileModulusConditions: { required: false, type: () => String }, shredding: { required: false, type: () => String }, overBand: { required: false, type: () => String }, washing: { required: false, type: () => String }, drying: { required: false, type: () => String }, manualSorting: { required: false, type: () => String }, densimetricSorting: { required: false, type: () => String }, opticalSorting: { required: false, type: () => String }, flotation: { required: false, type: () => String }, colorSorting: { required: false, type: () => String }, triboelectricSorting: { required: false, type: () => String }, aeraulicSorting: { required: false, type: () => String }, vacuumTreatmentAlongExtrusionLine: { required: false, type: () => String }, decantation: { required: false, type: () => String }, filtrationOrSize: { required: false, type: () => String }, degasification: { required: false, type: () => String }, homogenization: { required: false, type: () => String }, extrusionLine: { required: false, type: () => String }, lotSizeT: { required: false, type: () => String }, chronology: { required: false, type: () => String } } }], [import("./material/dto/material-page-response.dto"), { "MaterialPageResponseDto": { familyData: { required: false, type: () => Object } } }], [import("./material/dto/material-search-page-request.dto"), { "MaterialSearchPageRequestDto": { search: { required: false, type: () => String }, origin: { required: false, type: () => Object }, family: { required: true, type: () => Object }, status: { required: false, type: () => Object }, supplier: { required: false, type: () => String }, supplierBatchNumber: { required: false, type: () => String }, reference: { required: false, type: () => String }, page: { required: false, type: () => Number, minimum: 1 }, limit: { required: false, type: () => Number, minimum: 1, maximum: 100 }, familyCriteria: { required: false, type: () => [t["./material/dto/family-criteria.dto"].FamilyCriteriaDto] } } }], [import("./material/dto/material-search-request.dto"), { "MaterialSearchRequestDto": { search: { required: false, type: () => String }, origin: { required: false, type: () => String }, family: { required: false, type: () => Object }, status: { required: false, type: () => Object }, supplier: { required: false, type: () => String }, supplierBatchNumber: { required: false, type: () => String }, reference: { required: false, type: () => String }, page: { required: false, type: () => Number, default: 1, minimum: 1 }, limit: { required: false, type: () => Number, default: 10, minimum: 1, maximum: 100 } } }], [import("./material/dto/paginated-material-page-response.dto"), { "PaginatedMaterialPageResponseDto": { data: { required: true, type: () => [t["./material/dto/material-page-response.dto"].MaterialPageResponseDto] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./material/dto/paginated-material-response.dto"), { "PaginatedMaterialResponseDto": { data: { required: true, type: () => [t["./material/dto/material-search-response.dto"].MaterialSearchResponseDto] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./material/dto/update-material.dto"), { "UpdateMaterialDto": { type: { required: false, type: () => String }, reference: { required: false, type: () => String }, family: { required: true, type: () => Object }, supplier: { required: false, type: () => String }, supplierId: { required: false, type: () => String }, supplierBatchNumber: { required: true, type: () => String }, origin: { required: false, type: () => String }, status: { required: false, type: () => Object }, familyData: { required: false, type: () => Object } } }], [import("./material/dto/material-export-request.dto"), { "MaterialExportRequestDto": { selectedFields: { required: false, type: () => [String] }, statistics: { required: false, type: () => Boolean } } }], [import("./notification/dto/notification-response.dto"), { "NotificationResponseDto": { id: { required: true, type: () => String }, title: { required: true, type: () => String }, message: { required: true, type: () => String }, type: { required: true, type: () => Object }, isRead: { required: true, type: () => Boolean }, isSeen: { required: true, type: () => Boolean }, readAt: { required: true, type: () => Date, nullable: true }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date } } }], [import("./notification/dto/paginated-notification-response.dto"), { "PaginatedNotificationResponseDto": { data: { required: true, type: () => [t["./notification/dto/notification-response.dto"].NotificationResponseDto] }, unreadCount: { required: true, type: () => Number } } }], [import("./notification/dto/notification-query.dto"), { "NotificationQueryDto": { type: { required: false, type: () => Object }, isRead: { required: false, type: () => Boolean } } }], [import("./notification/dto/mark-as-read.dto"), { "MarkAsReadDto": { ids: { required: true, type: () => [String], minItems: 1 } } }], [import("./request/dto/create-request.dto"), { "CreateRequestDto": { formulationId: { required: true, type: () => String, format: "uuid" } } }], [import("./request/dto/department.dto"), { "DepartmentDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String } } }], [import("./request/dto/formulation.dto"), { "FormulationDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String }, ownerId: { required: true, type: () => String }, grade: { required: true, type: () => String } } }], [import("./request/dto/location.dto"), { "LocationDto": { id: { required: true, type: () => String }, city: { required: true, type: () => String }, country: { required: true, type: () => String } } }], [import("./request/dto/requester.dto"), { "RequesterDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String }, email: { required: true, type: () => String }, locationId: { required: true, type: () => String }, departmentId: { required: true, type: () => String }, roleId: { required: true, type: () => String }, createdAt: { required: true, type: () => String }, updatedAt: { required: true, type: () => String }, location: { required: true, type: () => t["./request/dto/location.dto"].LocationDto }, department: { required: true, type: () => t["./request/dto/department.dto"].DepartmentDto } } }], [import("./request/dto/request-response.dto"), { "RequestResponseDto": { id: { required: true, type: () => String }, status: { required: true, type: () => Object }, requester: { required: true, type: () => t["./request/dto/requester.dto"].RequesterDto }, formulation: { required: true, type: () => t["./request/dto/formulation.dto"].FormulationDto } } }], [import("./request/dto/paginated-request-response.dto"), { "PaginatedRequestResponseDto": { data: { required: true, type: () => [t["./request/dto/request-response.dto"].RequestResponseDto] } } }], [import("./request/dto/request-query.dto"), { "RequestQueryDto": { search: { required: false, type: () => String }, status: { required: false, type: () => Object } } }], [import("./request/dto/request-action.dto"), { "RequestActionDto": { action: { required: true, enum: t["./request/dto/request-action.enum"].REQUEST_ACTION } } }], [import("./specification/dto/specification-response.dto"), { "SpecificationResponseDto": { id: { required: true, type: () => String }, property: { required: true, type: () => String }, label: { required: true, type: () => String }, unit: { required: true, type: () => String, nullable: true }, type: { required: true, type: () => Object }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date } } }], [import("./specification/dto/paginated-specification-response.dto"), { "PaginatedSpecificationResponseDto": { data: { required: true, type: () => [t["./specification/dto/specification-response.dto"].SpecificationResponseDto] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }]], "controllers": [[import("./app.controller"), { "AppController": { "getHello": { type: String } } }], [import("./material/material.controller"), { "MaterialController": { "getMaterials": { type: t["./material/dto/paginated-material-response.dto"].PaginatedMaterialResponseDto }, "getMaterialOrigins": { type: [String] }, "getMaterialFilters": { type: t["./material/dto/material-filter-response.dto"].MaterialFilterResponseDto }, "getMaterialsPage": { type: t["./material/dto/paginated-material-page-response.dto"].PaginatedMaterialPageResponseDto }, "compareMaterials": { type: t["./material/dto/material-comparison-response.dto"].MaterialComparisonResponseDto }, "updateMaterial": { type: t["./material/dto/material-search-response.dto"].MaterialSearchResponseDto }, "batchUpdateMaterial": { type: [t["./material/dto/batch-update-material-response.dto"].BatchUpdateMaterialResponseDto] }, "exportMaterials": {} } }], [import("./formulation/formulation.controller"), { "FormulationController": { "createFormulations": { type: t["./formulation/dto/create-formulation-response.dto"].CreateFormulationResponseDto }, "getFormulations": { type: t["./formulation/dto/paginated-formulation-response.dto"].PaginatedFormulationResponseDto }, "getFormulationById": { type: t["./formulation/dto/formulation-response.dto"].FormulationResponseDto }, "compareFormulations": { type: t["./formulation/dto/comparison-response.dto"].ComparisonResponseDto }, "simulateFormulation": { type: t["./formulation/dto/create-simulation-response.dto"].CreateSimulationResponseDto } } }], [import("./notification/notification.controller"), { "NotificationController": { "getNotifications": { type: t["./notification/dto/paginated-notification-response.dto"].PaginatedNotificationResponseDto }, "markAsRead": { type: [t["./notification/dto/notification-response.dto"].NotificationResponseDto] } } }], [import("./user/user.controller"), { "UserController": { "create": { type: t["./user/dto/user-response.dto"].UserResponseDto }, "findAll": { type: t["./user/dto/paginated-user-response.dto"].PaginatedUserResponseDto }, "getFilterOptions": {}, "getCurrentUserProfile": { type: t["./user/dto/user-response.dto"].UserResponseDto }, "findOne": { type: t["./user/dto/user-response.dto"].UserResponseDto }, "update": { type: t["./user/dto/user-response.dto"].UserResponseDto }, "remove": {} } }], [import("./request/request.controller"), { "RequestController": { "findAll": { type: t["./request/dto/paginated-request-response.dto"].PaginatedRequestResponseDto }, "create": { type: t["./request/dto/request-response.dto"].RequestResponseDto }, "handleAction": { type: t["./request/dto/request-response.dto"].RequestResponseDto }, "revokeAccess": {} } }], [import("./specification/specification.controller"), { "SpecificationController": { "findAll": { type: t["./specification/dto/paginated-specification-response.dto"].PaginatedSpecificationResponseDto } } }]] } };
};