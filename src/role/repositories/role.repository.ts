import { Injectable } from "@nestjs/common";
import { PrismaService } from "@/prisma.service";

export interface RoleData {
  id: string
  name: string
}

@Injectable()
export class RoleRepository {
  public constructor(private readonly prisma: PrismaService) {}

  public async findById(id: string): Promise<RoleData | null> {
    return this.prisma.role.findUnique({
      where: { id },
    });
  }

  public async getRoleNames(): Promise<string[]> {
    const roles = await this.prisma.role.findMany({
      orderBy: { name: "asc" },
      select: { name: true },
    });
    return roles.map(role => role.name);
  }

  public async findAll(): Promise<RoleData[]> {
    return this.prisma.role.findMany({
      orderBy: { name: "asc" },
    });
  }

  public async findByCode(code: string): Promise<RoleData | null> {
    return this.prisma.role.findUnique({
      where: { code },
    });
  }
}
