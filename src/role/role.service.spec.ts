import { NotFoundException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { RoleRepository } from "./repositories";
import { RoleService } from "./role.service";

describe("RoleService", () => {
  let service: RoleService;

  const mockRole = { code: "TEST_ROLE", id: "1", name: "Test Role" };
  const mockRoles = [mockRole];
  const mockFindById = jest.fn().mockImplementation((id: string) => {
    if (id === "1") return Promise.resolve(mockRole);
    return Promise.resolve();
  });
  const mockFindAll = jest.fn().mockResolvedValue(mockRoles);
  const mockGetRoleNames = jest.fn().mockResolvedValue(["Test Role"]);
  const mockFindByCode = jest.fn().mockImplementation((code: string) => {
    if (code === "TEST_ROLE") return Promise.resolve(mockRole);
    return Promise.resolve();
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleService,
        {
          provide: RoleRepository,
          useValue: {
            findAll: mockFindAll,
            findByCode: mockFindByCode,
            findById: mockFindById,
            getRoleNames: mockGetRoleNames,
          },
        },
      ],
    }).compile();

    service = module.get<RoleService>(RoleService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("findOne", () => {
    it("should return a single role if found", async () => {
      const role = await service.findOne("1");
      expect(role).toEqual(mockRole);
      expect(mockFindById).toHaveBeenCalledWith("1");
    });

    it("should throw a NotFoundException if the role is not found", async () => {
      await expect(service.findOne("2")).rejects.toThrow(new NotFoundException("Role not found"));
    });
  });

  describe("findOneOrThrow", () => {
    it("should return a single role if found", async () => {
      const role = await service.findOneOrThrow("1");
      expect(role).toEqual(mockRole);
      expect(mockFindById).toHaveBeenCalledWith("1");
    });

    it("should throw a NotFoundException if the role is not found", async () => {
      await expect(service.findOneOrThrow("2")).rejects.toThrow(new NotFoundException("Role with ID '2' not found"));
    });
  });

  describe("getRoleNames", () => {
    it("should return an array of role names", async () => {
      const roleNames = await service.getRoleNames();
      expect(roleNames).toEqual(["Test Role"]);
      expect(mockGetRoleNames).toHaveBeenCalled();
    });
  });

  describe("findAll", () => {
    it("should return an array of roles", async () => {
      const roles = await service.findAll();
      expect(roles).toEqual(mockRoles);
      expect(mockFindAll).toHaveBeenCalled();
    });
  });

  describe("findByCode", () => {
    it("should return a single role if found", async () => {
      const role = await service.findByCode("TEST_ROLE");
      expect(role).toEqual(mockRole);
      expect(mockFindByCode).toHaveBeenCalledWith("TEST_ROLE");
    });

    it("should throw a NotFoundException if the role is not found", async () => {
      await expect(service.findByCode("INVALID_CODE")).rejects.toThrow(new NotFoundException("Role with code 'INVALID_CODE' not found"));
    });
  });
});
