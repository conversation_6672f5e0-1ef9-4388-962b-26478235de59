import { Injectable, NotFoundException } from "@nestjs/common";
import { LocationResponseDto } from "./dto/location-response.dto";
import { LocationRepository } from "./repositories/location.repository";

@Injectable()
export class LocationService {
  public constructor(private readonly locationRepository: LocationRepository) {}

  public async findAll(): Promise<LocationResponseDto[]> {
    const locations = await this.locationRepository.findAll();
    return locations.map(location => this.mapToResponseDto(location));
  }

  public async findOne(id: string): Promise<LocationResponseDto> {
    const location = await this.locationRepository.findOne(id);
    if (!location) {
      throw new NotFoundException(`Location with ID "${id}" not found`);
    }
    return this.mapToResponseDto(location);
  }

  public async findOneOrThrow(id: string): Promise<LocationResponseDto> {
    const location = await this.locationRepository.findOne(id);

    if (!location) {
      throw new NotFoundException(`Location with ID '${id}' not found`);
    }

    return this.mapToResponseDto(location);
  }

  private mapToResponseDto(location: { city: string, country: string, id: string }): LocationResponseDto {
    return {
      city: location.city,
      country: location.country,
      id: location.id,
    };
  }
}
