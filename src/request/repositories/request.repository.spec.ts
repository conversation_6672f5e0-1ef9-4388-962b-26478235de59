import { UnprocessableEntityException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { RequestRepository } from "./request.repository";
import { SearchQueryBuilder } from "./search-query.builder";
import { RequestStatus } from "@/generated/prisma";
import { PrismaService } from "@/prisma.service";

interface PrismaRequestMock {
  count: jest.Mock
  create: jest.Mock
  delete: jest.Mock
  findMany: jest.Mock
  findUnique: jest.Mock
  update: jest.Mock
}
interface PrismaMock {
  request: PrismaRequestMock
}
interface SearchQueryBuilderMock {
  searchRequestWithRelations: jest.Mock
}

describe("RequestRepository", () => {
  let repo: RequestRepository;
  let prisma: PrismaMock;
  let searchQueryBuilder: SearchQueryBuilderMock;

  beforeEach(async () => {
    prisma = {
      request: {
        count: jest.fn(),
        create: jest.fn(),
        delete: jest.fn(),
        findMany: jest.fn(),
        findUnique: jest.fn(),
        update: jest.fn(),
      },
    };
    searchQueryBuilder = {
      searchRequestWithRelations: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RequestRepository,
        { provide: PrismaService, useValue: prisma },
        { provide: SearchQueryBuilder, useValue: searchQueryBuilder },
      ],
    }).compile();

    repo = module.get(RequestRepository);
  });

  it("findAll should return paginated requests with search", async () => {
    searchQueryBuilder.searchRequestWithRelations.mockResolvedValue([{ id: "r1" }]);
    prisma.request.findMany.mockResolvedValue([{ id: "r1" }]);
    prisma.request.count.mockResolvedValue(1);

    const result = await repo.findAll({ limit: 5, page: 2, search: "foo" });
    expect(searchQueryBuilder.searchRequestWithRelations).toHaveBeenCalledWith("foo");
    expect(prisma.request.findMany).toHaveBeenCalledWith(expect.objectContaining({
      orderBy: { id: "desc" },
      skip: 5,
      take: 5,
      where: { id: { in: ["r1"] } },
    }));
    expect(result).toEqual({ data: [{ id: "r1" }], limit: 5, page: 2, total: 1 });
  });

  it("findAll should return empty if search yields no ids", async () => {
    searchQueryBuilder.searchRequestWithRelations.mockResolvedValue([]);
    const result = await repo.findAll({ search: "foo" });
    expect(result).toEqual({ data: [], limit: 10, page: 1, total: 0 });
  });

  it("findAll should filter by status", async () => {
    prisma.request.findMany.mockResolvedValue([{ id: "r2" }]);
    prisma.request.count.mockResolvedValue(1);
    const result = await repo.findAll({ status: RequestStatus.PENDING_APPROVAL });
    expect(prisma.request.findMany).toHaveBeenCalledWith(expect.objectContaining({
      where: { status: RequestStatus.PENDING_APPROVAL },
    }));
    expect(result.data.length).toBe(1);
    if (result.data[0]) {
      expect(result.data[0].id).toBe("r2");
    }
  });

  it("create should create a request", async () => {
    prisma.request.create.mockResolvedValue({ id: "r3" });
    const result = await repo.create("u1", "f1");
    expect(prisma.request.create).toHaveBeenCalledWith(expect.objectContaining({
      data: { formulationId: "f1", requesterId: "u1", status: RequestStatus.PENDING_APPROVAL },
    }));
    expect(result.id).toBe("r3");
  });

  it("create should throw UnprocessableEntityException on duplicate", async () => {
    prisma.request.create.mockRejectedValue({ code: "P2002" });
    await expect(repo.create("u1", "f1")).rejects.toThrow(UnprocessableEntityException);
  });

  it("findById should query by id", async () => {
    prisma.request.findUnique.mockResolvedValue({ id: "r4" });
    const result = await repo.findById("r4");
    expect(prisma.request.findUnique).toHaveBeenCalledWith(expect.objectContaining({
      where: { id: "r4" },
    }));
    if (result) {
      expect(result.id).toBe("r4");
    }
  });

  it("updateStatus should update request status", async () => {
    prisma.request.update.mockResolvedValue({ id: "r5", status: RequestStatus.APPROVED });
    await repo.updateStatus("r5", RequestStatus.APPROVED);
    expect(prisma.request.update).toHaveBeenCalledWith(expect.objectContaining({
      data: { status: RequestStatus.APPROVED },
      where: { id: "r5" },
    }));
    await repo.delete("r6");
    expect(prisma.request.delete).toHaveBeenCalledWith({ where: { id: "r6" } });
  });
});
