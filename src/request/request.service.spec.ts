import { BadRequestException, NotFoundException } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { Test, TestingModule } from "@nestjs/testing";
import { REQUEST_ACTION } from "./dto/request-action.enum";
import { RequestFilters, RequestRepository, RequestWithRelations } from "./repositories";
import { RequestService } from "./request.service";
import { EmailService } from "@/email/email.service";
import { RequestStatus } from "@/generated/prisma";
import { NotificationService } from "@/notification";
import { PrismaService } from "@/prisma.service";
import { RoleService } from "@/role";
import { UserService } from "@/user";

class MockEventEmitter {
  public emit(): undefined {
    return undefined;
  }
}

describe("RequestService", () => {
  let service: RequestService;
  let requestRepository: {
    create: jest.MockedFunction<(requesterId: string, formulationId: string) => Promise<RequestWithRelations>>
    delete: jest.MockedFunction<(id: string) => Promise<void>>
    findAll: jest.MockedFunction<(filters: RequestFilters) => Promise<{ data: RequestWithRelations[], total: number }>>
    findById: jest.MockedFunction<(id: string) => Promise<RequestWithRelations>>
    updateStatus: jest.MockedFunction<(id: string, status: RequestStatus) => Promise<RequestWithRelations>>
  };
  let prisma: {
    formulation: {
      findUnique: jest.MockedFunction<(arguments_: { where: { id: string } }) => Promise<{ id: string, name?: string, ownerId: string } | undefined>>
    }
  };
  let userService: {
    findAll: jest.MockedFunction<(filters: unknown) => Promise<unknown>>
  };
  let roleService: {
    findByCode: jest.MockedFunction<(code: string) => Promise<{ id: string }>>
  };
  let eventEmitter: EventEmitter2;

  const mockRequest = {
    formulation: { code: "FA001", grade: "A", id: "f1", name: "FormA", ownerId: "owner1" },
    formulationId: "f1",
    id: "r1",
    requester: {
      createdAt: new Date("2023-01-01T00:00:00.000Z"),
      department: {
        id: "dept1",
        name: "Engineering",
      },
      departmentId: "dept1",
      email: "<EMAIL>",
      id: "user1",
      location: {
        city: "New York",
        country: "USA",
        id: "loc1",
      },
      locationId: "loc1",
      name: "John Doe",
      roleId: "role1",
      updatedAt: new Date("2023-01-01T00:00:00.000Z"),
    },
    requesterId: "user1",
    status: RequestStatus.PENDING_APPROVAL,
  };

  beforeEach(async () => {
    requestRepository = {
      create: jest.fn().mockResolvedValue(mockRequest),
      delete: jest.fn(),
      findAll: jest.fn().mockResolvedValue({ data: [mockRequest], total: 1 }),
      findById: jest.fn().mockResolvedValue(mockRequest),
      updateStatus: jest.fn().mockImplementation((id: string, status: RequestStatus) => Promise.resolve({ ...mockRequest, status })),
    };
    prisma = { formulation: { findUnique: jest.fn() } };
    userService = { findAll: jest.fn() };
    roleService = { findByCode: jest.fn() };

    const moduleReference: TestingModule = await Test.createTestingModule({
      providers: [
        { provide: EmailService, useValue: {} },
        { provide: EventEmitter2, useClass: MockEventEmitter },
        { provide: NotificationService, useValue: {} },
        { provide: PrismaService, useValue: prisma },
        { provide: RequestRepository, useValue: requestRepository },
        { provide: RoleService, useValue: roleService },
        { provide: UserService, useValue: userService },
        RequestService,
      ],
    }).compile();
    service = moduleReference.get<RequestService>(RequestService);
    eventEmitter = moduleReference.get<EventEmitter2>(EventEmitter2);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("findAll", () => {
    it("should return paginated requests", async () => {
      const result = await service.findAll({});
      expect(requestRepository.findAll).toHaveBeenCalled();
      expect(result.data[0].id).toBe("r1");
    });
  });

  describe("create", () => {
    it("should throw if role not found", async () => {
      prisma.formulation.findUnique.mockResolvedValue({ id: "", name: "", ownerId: "" });
      await expect(service.create("user1", { formulationId: "f1" })).rejects.toThrow("Role not found");
    });

    it("should create request and emit events for email/notification", async () => {
      prisma.formulation.findUnique.mockResolvedValue({ id: "f1", name: "FormA", ownerId: "owner1" });
      roleService.findByCode.mockResolvedValue({ id: "role1" });
      userService.findAll.mockResolvedValue({ data: [{ email: "<EMAIL>", id: "manager1" }] });
      const emitSpy = jest.spyOn(eventEmitter, "emit");

      const result = await service.create("user1", { formulationId: "f1" });
      expect(result.id).toBe("r1");
      expect(emitSpy).toHaveBeenCalledWith(
        "request.new-formulation",
        expect.objectContaining({
          formulationName: "FormA",
          html: expect.any(String) as string,
          managerEmails: ["<EMAIL>"],
          managerIds: ["manager1"],
          message: expect.any(String) as string,
          requesterId: "user1",
        })
      );
    });
  });

  describe("handleAction", () => {
    it("should approve a request", async () => {
      const emitSpy = jest.spyOn(eventEmitter, "emit");
      const result = await service.handleAction("user1", "r1", REQUEST_ACTION.APPROVE);
      expect(requestRepository.updateStatus).toHaveBeenCalledWith("r1", RequestStatus.APPROVED);
      expect(result.status).toBe(RequestStatus.APPROVED);
      expect(emitSpy).toHaveBeenCalledWith("request.status-changed", expect.any(Object));
    });

    it("should reject a request", async () => {
      const emitSpy = jest.spyOn(eventEmitter, "emit");
      const result = await service.handleAction("user1", "r1", REQUEST_ACTION.REJECT);
      expect(requestRepository.updateStatus).toHaveBeenCalledWith("r1", RequestStatus.REJECTED);
      expect(result.status).toBe(RequestStatus.REJECTED);
      expect(emitSpy).toHaveBeenCalledWith("request.status-changed", expect.any(Object));
    });

    it("should throw NotFoundException if request not found", async () => {
      requestRepository.findById.mockResolvedValue(undefined as unknown as RequestWithRelations);
      await expect(service.handleAction("user1", "r2", REQUEST_ACTION.APPROVE)).rejects.toThrow(NotFoundException);
    });

    it("should throw BadRequestException if request is not pending approval", async () => {
      requestRepository.findById.mockResolvedValue({ ...mockRequest, status: RequestStatus.APPROVED });
      await expect(service.handleAction("user1", "r1", REQUEST_ACTION.APPROVE)).rejects.toThrow(BadRequestException);
    });

    it("should throw BadRequestException for invalid action", async () => {
      await expect(service.handleAction("user1", "r1", "invalid_action" as REQUEST_ACTION)).rejects.toThrow(BadRequestException);
    });
  });

  describe("revokeAccess", () => {
    it("should delete a request", async () => {
      await service.revokeAccess("user1", "r1");
      expect(requestRepository.delete).toHaveBeenCalledWith("r1");
    });

    it("should throw NotFoundException if request not found", async () => {
      requestRepository.findById.mockResolvedValue(undefined as unknown as RequestWithRelations);
      await expect(service.revokeAccess("user1", "r2")).rejects.toThrow(NotFoundException);
    });
  });
});
