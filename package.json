{"name": "materiact-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "npx prisma generate && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix && prisma format", "lint:json": "eslint \"{src,apps,libs,test}/**/*.ts\" --format json --output-file eslint-report.json", "test": "jest", "test:watch": "jest --watch", "test:cov": "node --max-old-space-size=4096 node_modules/.bin/jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky", "prisma": "prisma"}, "dependencies": {"@azure/communication-email": "^1.0.0", "@azure/monitor-opentelemetry-exporter": "^1.0.0-beta.32", "@fastify/static": "^8.2.0", "@nestjs/common": "^11.1.6", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.6", "@nestjs/event-emitter": "^3.0.1", "@nestjs/platform-fastify": "^11.1.6", "@nestjs/swagger": "^11.2.0", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@opentelemetry/sdk-logs": "^0.203.0", "@opentelemetry/sdk-node": "^0.203.0", "@opentelemetry/winston-transport": "^0.14.0", "@prisma/client": "^6.13.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "nest-winston": "^1.10.2", "prisma-extension-soft-delete": "^2.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.33.0", "@nestjs/cli": "^11.0.10", "@nestjs/schematics": "^11.0.7", "@nestjs/testing": "^11.1.6", "@stylistic/eslint-plugin": "^5.2.3", "@swc/cli": "^0.7.8", "@swc/core": "^1.13.3", "@types/date-fns": "^2.6.3", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.2.1", "@types/supertest": "^6.0.3", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsdoc": "^52.0.4", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-sort-class-members": "^1.21.0", "eslint-plugin-sort-keys-custom-order": "^2.2.1", "eslint-plugin-unicorn": "^60.0.0", "globals": "^16.3.0", "husky": "^9.1.7", "jest": "^30.0.5", "prettier": "^3.6.2", "prisma": "^6.13.0", "source-map-support": "^0.5.21", "supertest": "^7.1.4", "ts-jest": "^29.4.1", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.9.2", "typescript-eslint": "^8.39.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.ts$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@/generated/(.*)$": "<rootDir>/../generated/$1", "^@/(.*)$": "<rootDir>/$1"}}, "prisma": {"seed": "ts-node prisma/seed.ts"}}