import { USER_ROLE } from "../../src/role/role.types";
import { PrismaClient } from "../../generated/prisma";

/**
 * Seed basic foundational data: roles, locations, departments, and users
 * @param prisma - The Prisma client instance
 * @returns Object containing created entities for use in other seed functions
 */
export async function seedBasicData(prisma: PrismaClient) {
  const adminRole = await prisma.role.create({
    data: {
      name: "Admin", code: USER_ROLE.ADMIN,
    },
  });

  const dataScientistRole = await prisma.role.create({
    data: { name: "Data Scientist", code: USER_ROLE.DATA_SCIENTIST },
  });

  const engineeringManagerRole = await prisma.role.create({
    data: { name: "Engineering Manager", code: USER_ROLE.ENGINEERING_MANAGER },
  });

  const engineersRole = await prisma.role.create({
    data: { name: "Engineers", code: USER_ROLE.ENGINEERS },
  });

  const feedbackMembersRole = await prisma.role.create({
    data: { name: "Feedback Members", code: USER_ROLE.FEEDSTOCK_RECYCLING_MEMBERS },
  });

  const parisLocation = await prisma.location.create({
    data: { city: "Paris", country: "France" },
  });

  const technologyDept = await prisma.department.create({
    data: { name: "Technology" },
  });

  const engineeringDept = await prisma.department.create({
    data: { name: "Engineering" },
  });

  const dataScientistDept = await prisma.department.create({
    data: { name: "Data Scientist" },
  });

  const omar = await prisma.user.create({
    data: {
      name: "Omar Press",
      email: "<EMAIL>",
      locationId: parisLocation.id,
      departmentId: technologyDept.id,
      roleId: adminRole.id,
    },
  });

  const nolan = await prisma.user.create({
    data: {
      name: "Nolan Siphron",
      email: "<EMAIL>",
      locationId: parisLocation.id,
      departmentId: technologyDept.id,
      roleId: dataScientistRole.id,
    },
  });

  const roger = await prisma.user.create({
    data: {
      name: "Roger Press",
      email: "<EMAIL>",
      locationId: parisLocation.id,
      departmentId: engineeringDept.id,
      roleId: engineeringManagerRole.id,
    },
  });

  await prisma.user.create({
    data: {
      name: "Ahmad Bator",
      email: "<EMAIL>",
      locationId: parisLocation.id,
      departmentId: engineeringDept.id,
      roleId: engineersRole.id,
    },
  });

  await prisma.user.create({
    data: {
      name: "Ahmad Bator",
      email: "<EMAIL>",
      locationId: parisLocation.id,
      departmentId: dataScientistDept.id,
      roleId: feedbackMembersRole.id,
    },
  });

  await prisma.user.create({
    data: {
      name: "MA Admin",
      email: "<EMAIL>",
      locationId: parisLocation.id,
      departmentId: dataScientistDept.id,
      roleId: adminRole.id,
    },
  });

  return {
    users: { omar, nolan, roger },
    roles: { adminRole, dataScientistRole, engineeringManagerRole, engineersRole, feedbackMembersRole },
    locations: { parisLocation },
    departments: { technologyDept, engineeringDept, dataScientistDept },
  };
}
